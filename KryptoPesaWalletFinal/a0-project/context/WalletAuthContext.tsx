import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { userService, walletAuthService } from '../services';
import { log, logError } from '../config/environment';
import { MobileWalletUtils } from '../utils/walletUtils';
// import * as LocalAuthentication from 'expo-local-authentication';

interface WalletData {
  addresses: {
    ethereum: string;
    bitcoin: string;
    polygon: string;
  };
  isBackedUp: boolean;
  createdAt: string;
}

interface UserProfile {
  id: string;
  name?: string;
  country?: string;
  city?: string;
  profileComplete: boolean;
  kycLevel: number;
  reputation: number;
  totalTrades: number;
  // Backend sync status
  backendSynced?: boolean;
  lastSyncAt?: string;
}

interface WalletAuthContextType {
  // Wallet state
  isWalletSetup: boolean;
  isWalletUnlocked: boolean;
  walletData: WalletData | null;
  userProfile: UserProfile | null;

  // Loading states
  isLoading: boolean;
  isInitializing: boolean;
  isSyncing: boolean;

  // Wallet actions
  createWallet: (mnemonic: string[], pin: string) => Promise<void>;
  importWallet: (mnemonic: string[], pin: string) => Promise<void>;
  unlockWallet: (pin: string) => Promise<boolean>;
  lockWallet: () => void;

  // Profile actions
  updateProfile: (profile: Partial<UserProfile>) => Promise<void>;

  // Security actions
  verifyPin: (pin: string) => Promise<boolean>;
  changePIN: (oldPin: string, newPin: string) => Promise<boolean>;

  // Biometric actions
  isBiometricsEnabled: boolean;
  enableBiometrics: () => Promise<boolean>;
  disableBiometrics: () => Promise<void>;
  authenticateWithBiometrics: () => Promise<boolean>;

  // Backend sync
  syncWithBackend: () => Promise<boolean>;
  isBackendSynced: boolean;
}

const WalletAuthContext = createContext<WalletAuthContextType | undefined>(undefined);

// Storage keys
const STORAGE_KEYS = {
  WALLET_EXISTS: 'wallet_exists',
  WALLET_DATA: 'wallet_data',
  USER_PROFILE: 'user_profile',
  PIN_HASH: 'pin_hash',
  BIOMETRICS_ENABLED: 'biometrics_enabled',
  ENCRYPTED_MNEMONIC: 'encrypted_mnemonic',
  BACKEND_SYNCED: 'backend_synced',
  LAST_SYNC_TIME: 'last_sync_time',
};

interface WalletAuthProviderProps {
  children: ReactNode;
}

export function WalletAuthProvider({ children }: WalletAuthProviderProps) {
  const [isWalletSetup, setIsWalletSetup] = useState(false);
  const [isWalletUnlocked, setIsWalletUnlocked] = useState(false);
  const [walletData, setWalletData] = useState<WalletData | null>(null);
  const [userProfile, setUserProfile] = useState<UserProfile | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [isInitializing, setIsInitializing] = useState(true);
  const [isBiometricsEnabled, setIsBiometricsEnabled] = useState(false);
  const [isSyncing, setIsSyncing] = useState(false);
  const [isBackendSynced, setIsBackendSynced] = useState(false);

  useEffect(() => {
    initializeWallet();
  }, []);

  const initializeWallet = async () => {
    try {
      setIsInitializing(true);

      // Check if wallet exists
      const walletExists = await AsyncStorage.getItem(STORAGE_KEYS.WALLET_EXISTS);
      setIsWalletSetup(walletExists === 'true');

      if (walletExists === 'true') {
        // Load wallet data and user profile
        const [storedWalletData, storedProfile, biometricsEnabled, syncStatus] = await Promise.all([
          AsyncStorage.getItem(STORAGE_KEYS.WALLET_DATA),
          AsyncStorage.getItem(STORAGE_KEYS.USER_PROFILE),
          AsyncStorage.getItem(STORAGE_KEYS.BIOMETRICS_ENABLED),
          AsyncStorage.getItem(STORAGE_KEYS.BACKEND_SYNCED),
        ]);

        if (storedWalletData) {
          setWalletData(JSON.parse(storedWalletData));
        }

        if (storedProfile) {
          setUserProfile(JSON.parse(storedProfile));
        }

        setIsBiometricsEnabled(biometricsEnabled === 'true');
        setIsBackendSynced(syncStatus === 'true');

        // Try to sync with backend if we have wallet data
        if (storedWalletData && !syncStatus) {
          // Don't await this to avoid blocking initialization
          syncWithBackend().catch(err => logError('Background sync failed:', err));
        }
      }
    } catch (error) {
      logError('Failed to initialize wallet:', error);
    } finally {
      setIsInitializing(false);
    }
  };

  const generateWalletAddresses = (mnemonic: string[]): WalletData['addresses'] => {
    try {
      // Use real crypto libraries to derive addresses
      return MobileWalletUtils.generateWalletAddresses(mnemonic);
    } catch (error) {
      logError('Error generating wallet addresses:', error);
      throw new Error('Failed to generate wallet addresses');
    }
  };

  const hashPin = (pin: string): string => {
    try {
      // Use proper crypto hashing
      return MobileWalletUtils.hashPin(pin);
    } catch (error) {
      logError('Error hashing PIN:', error);
      throw new Error('Failed to hash PIN');
    }
  };

  const createWallet = async (mnemonic: string[], pin: string) => {
    try {
      setIsLoading(true);

      const addresses = generateWalletAddresses(mnemonic);
      const newWalletData: WalletData = {
        addresses,
        isBackedUp: true,
        createdAt: new Date().toISOString(),
      };

      const newProfile: UserProfile = {
        id: `user_${Date.now()}`,
        profileComplete: false,
        kycLevel: 0,
        reputation: 0,
        totalTrades: 0,
        backendSynced: false,
      };

      // Store wallet data
      await Promise.all([
        AsyncStorage.setItem(STORAGE_KEYS.WALLET_EXISTS, 'true'),
        AsyncStorage.setItem(STORAGE_KEYS.WALLET_DATA, JSON.stringify(newWalletData)),
        AsyncStorage.setItem(STORAGE_KEYS.USER_PROFILE, JSON.stringify(newProfile)),
        AsyncStorage.setItem(STORAGE_KEYS.PIN_HASH, hashPin(pin)),
        // In production, encrypt the mnemonic properly
        AsyncStorage.setItem(STORAGE_KEYS.ENCRYPTED_MNEMONIC, JSON.stringify(mnemonic)),
      ]);

      setIsWalletSetup(true);
      setWalletData(newWalletData);
      setUserProfile(newProfile);
      setIsWalletUnlocked(true);

      // Try to sync with backend
      syncWithBackend().catch(err => logError('Initial sync failed:', err));
    } catch (error) {
      logError('Failed to create wallet:', error);
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  const importWallet = async (mnemonic: string[], pin: string) => {
    try {
      setIsLoading(true);

      const addresses = generateWalletAddresses(mnemonic);
      const importedWalletData: WalletData = {
        addresses,
        isBackedUp: true,
        createdAt: new Date().toISOString(),
      };

      const newProfile: UserProfile = {
        id: `user_${Date.now()}`,
        profileComplete: false,
        kycLevel: 0,
        reputation: 0,
        totalTrades: 0,
      };

      // Store wallet data
      await Promise.all([
        AsyncStorage.setItem(STORAGE_KEYS.WALLET_EXISTS, 'true'),
        AsyncStorage.setItem(STORAGE_KEYS.WALLET_DATA, JSON.stringify(importedWalletData)),
        AsyncStorage.setItem(STORAGE_KEYS.USER_PROFILE, JSON.stringify(newProfile)),
        AsyncStorage.setItem(STORAGE_KEYS.PIN_HASH, hashPin(pin)),
        AsyncStorage.setItem(STORAGE_KEYS.ENCRYPTED_MNEMONIC, JSON.stringify(mnemonic)),
      ]);

      setIsWalletSetup(true);
      setWalletData(importedWalletData);
      setUserProfile(newProfile);
      setIsWalletUnlocked(true);
    } catch (error) {
      console.error('Failed to import wallet:', error);
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  const unlockWallet = async (pin: string): Promise<boolean> => {
    try {
      const storedPinHash = await AsyncStorage.getItem(STORAGE_KEYS.PIN_HASH);
      const enteredPinHash = hashPin(pin);

      if (storedPinHash === enteredPinHash) {
        setIsWalletUnlocked(true);
        return true;
      }
      return false;
    } catch (error) {
      console.error('Failed to unlock wallet:', error);
      return false;
    }
  };

  const lockWallet = () => {
    setIsWalletUnlocked(false);
  };

  const updateProfile = async (profileUpdates: Partial<UserProfile>) => {
    try {
      if (!userProfile) return;

      const updatedProfile = { ...userProfile, ...profileUpdates };
      await AsyncStorage.setItem(STORAGE_KEYS.USER_PROFILE, JSON.stringify(updatedProfile));
      setUserProfile(updatedProfile);
    } catch (error) {
      console.error('Failed to update profile:', error);
      throw error;
    }
  };

  const verifyPin = async (pin: string): Promise<boolean> => {
    try {
      const storedPinHash = await AsyncStorage.getItem(STORAGE_KEYS.PIN_HASH);
      return storedPinHash === hashPin(pin);
    } catch (error) {
      console.error('Failed to verify PIN:', error);
      return false;
    }
  };

  const changePIN = async (oldPin: string, newPin: string): Promise<boolean> => {
    try {
      const isOldPinValid = await verifyPin(oldPin);
      if (!isOldPinValid) return false;

      await AsyncStorage.setItem(STORAGE_KEYS.PIN_HASH, hashPin(newPin));
      return true;
    } catch (error) {
      console.error('Failed to change PIN:', error);
      return false;
    }
  };

  const enableBiometrics = async (): Promise<boolean> => {
    try {
      // Mock implementation for now
      await AsyncStorage.setItem(STORAGE_KEYS.BIOMETRICS_ENABLED, 'true');
      setIsBiometricsEnabled(true);
      return true;
    } catch (error) {
      console.error('Failed to enable biometrics:', error);
      return false;
    }
  };

  const disableBiometrics = async () => {
    try {
      await AsyncStorage.setItem(STORAGE_KEYS.BIOMETRICS_ENABLED, 'false');
      setIsBiometricsEnabled(false);
    } catch (error) {
      console.error('Failed to disable biometrics:', error);
    }
  };

  const authenticateWithBiometrics = async (): Promise<boolean> => {
    try {
      if (!isBiometricsEnabled) return false;

      // Mock implementation for now
      setIsWalletUnlocked(true);
      return true;
    } catch (error) {
      logError('Biometric authentication failed:', error);
      return false;
    }
  };

  // Sync wallet data with backend
  const syncWithBackend = async (): Promise<boolean> => {
    try {
      if (!walletData || !userProfile) {
        log('No wallet data to sync');
        return false;
      }

      setIsSyncing(true);

      // Real signing function for backend authentication
      const signMessage = async (message: string): Promise<string> => {
        try {
          // Get the stored mnemonic to sign the message
          const storedMnemonic = await AsyncStorage.getItem(STORAGE_KEYS.WALLET_DATA);
          if (!storedMnemonic) {
            throw new Error('No wallet mnemonic found');
          }

          const walletDataParsed = JSON.parse(storedMnemonic);
          if (!walletDataParsed.mnemonic) {
            throw new Error('No mnemonic in wallet data');
          }

          // Use real wallet signing
          return await MobileWalletUtils.signMessage(walletDataParsed.mnemonic, message);
        } catch (error) {
          logError('Error signing message:', error);
          throw new Error('Failed to sign authentication message');
        }
      };

      // Authenticate with backend using primary wallet address
      const primaryAddress = walletData.addresses.ethereum;
      log('Syncing wallet with backend:', primaryAddress);

      try {
        const authResponse = await walletAuthService.authenticateWithWallet(
          primaryAddress,
          signMessage
        );

        if (authResponse) {
          // Try to get existing profile from backend
          try {
            const backendProfile = await userService.getProfile();

            // Merge local profile with backend profile
            const mergedProfile: UserProfile = {
              ...userProfile,
              backendSynced: true,
              lastSyncAt: new Date().toISOString(),
              // Keep local data but update with backend data if available
              reputation: backendProfile.reputation?.score || userProfile.reputation,
              totalTrades: backendProfile.reputation?.totalTrades || userProfile.totalTrades,
            };

            // Update local storage
            await Promise.all([
              AsyncStorage.setItem(STORAGE_KEYS.USER_PROFILE, JSON.stringify(mergedProfile)),
              AsyncStorage.setItem(STORAGE_KEYS.BACKEND_SYNCED, 'true'),
              AsyncStorage.setItem(STORAGE_KEYS.LAST_SYNC_TIME, new Date().toISOString()),
            ]);

            setUserProfile(mergedProfile);
            setIsBackendSynced(true);

            log('Backend sync successful');
            return true;
          } catch (profileError) {
            // Profile doesn't exist on backend, this is fine for new users
            log('Profile not found on backend, user may be new');

            // Mark as synced anyway since authentication worked
            const syncedProfile = {
              ...userProfile,
              backendSynced: true,
              lastSyncAt: new Date().toISOString(),
            };

            await Promise.all([
              AsyncStorage.setItem(STORAGE_KEYS.USER_PROFILE, JSON.stringify(syncedProfile)),
              AsyncStorage.setItem(STORAGE_KEYS.BACKEND_SYNCED, 'true'),
              AsyncStorage.setItem(STORAGE_KEYS.LAST_SYNC_TIME, new Date().toISOString()),
            ]);

            setUserProfile(syncedProfile);
            setIsBackendSynced(true);
            return true;
          }
        }

        return false;
      } catch (authError) {
        logError('Backend authentication failed:', authError);
        return false;
      }
    } catch (error) {
      logError('Backend sync failed:', error);
      return false;
    } finally {
      setIsSyncing(false);
    }
  };

  const value: WalletAuthContextType = {
    // State
    isWalletSetup,
    isWalletUnlocked,
    walletData,
    userProfile,
    isLoading,
    isInitializing,
    isBiometricsEnabled,
    isSyncing,
    isBackendSynced,

    // Actions
    createWallet,
    importWallet,
    unlockWallet,
    lockWallet,
    updateProfile,
    verifyPin,
    changePIN,
    enableBiometrics,
    disableBiometrics,
    authenticateWithBiometrics,
    syncWithBackend,
  };

  return (
    <WalletAuthContext.Provider value={value}>
      {children}
    </WalletAuthContext.Provider>
  );
}

export function useWalletAuth() {
  const context = useContext(WalletAuthContext);
  if (context === undefined) {
    throw new Error('useWalletAuth must be used within a WalletAuthProvider');
  }
  return context;
}
