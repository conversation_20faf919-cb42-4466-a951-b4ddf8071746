import React, { createContext, useContext, useState, useEffect } from 'react';
import { Alert } from 'react-native';
import { walletAuthService, userService, authTokens } from '../services';
import { log, logError } from '../config/environment';

// Define user types
export interface User {
  _id: string;
  walletAddress: string;
  profile: {
    displayName?: string;
    email?: string;
    phone?: string;
    location?: {
      country?: string;
      city?: string;
    };
    avatar?: string;
  };
  reputation: {
    score: number;
    totalTrades: number;
    completionRate?: number;
    averageResponseTime?: number;
  };
  preferences: {
    currency: string;
    language: string;
    notifications: {
      email: boolean;
      push: boolean;
      sms: boolean;
    };
  };
  security: {
    twoFactorEnabled: boolean;
    kycVerified: boolean;
    kycLevel: number;
    lastLogin?: string;
  };
  createdAt: string;
  updatedAt: string;
}

// Define the shape of our auth context
interface AuthContextType {
  // State
  user: User | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  isAuthenticating: boolean;
  authError: string | null;

  // Wallet Authentication
  connectWallet: (walletAddress: string, signMessage: (message: string) => Promise<string>) => Promise<boolean>;
  disconnectWallet: () => Promise<void>;

  // Session Management
  refreshSession: () => Promise<boolean>;

  // User Profile
  updateUserProfile: (profileData: Partial<User['profile']>) => Promise<boolean>;

  // Utility Methods
  getWalletAddress: () => Promise<string | null>;
}

// Create the context with a default value
const AuthContext = createContext<AuthContextType>({
  user: null,
  isAuthenticated: false,
  isLoading: true,
  isAuthenticating: false,
  authError: null,
  connectWallet: async () => false,
  disconnectWallet: async () => { },
  refreshSession: async () => false,
  updateUserProfile: async () => false,
  getWalletAddress: async () => null,
});

// Create a provider component
export const AuthProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  // State
  const [user, setUser] = useState<User | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isAuthenticating, setIsAuthenticating] = useState(false);
  const [authError, setAuthError] = useState<string | null>(null);

  // Connect wallet and authenticate with backend
  const connectWallet = async (
    walletAddress: string,
    signMessage: (message: string) => Promise<string>
  ): Promise<boolean> => {
    try {
      setIsAuthenticating(true);
      setAuthError(null);

      log('Connecting wallet:', walletAddress);

      // Authenticate with the wallet
      const authResponse = await walletAuthService.authenticateWithWallet(
        walletAddress,
        signMessage
      );

      if (authResponse) {
        // Get user profile after successful authentication
        const userProfile = await userService.getProfile();
        setUser(userProfile);
        log('Wallet authentication successful');
        return true;
      }

      return false;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to connect wallet';
      logError('Wallet connection error:', error);
      setAuthError(errorMessage);
      Alert.alert('Authentication Error', errorMessage);
      return false;
    } finally {
      setIsAuthenticating(false);
    }
  };

  // Disconnect wallet and clear session
  const disconnectWallet = async (): Promise<void> => {
    try {
      setIsLoading(true);

      // Call logout endpoint
      await walletAuthService.logout();

      // Clear local tokens
      await authTokens.clearTokens();

      // Clear user state
      setUser(null);

      log('Wallet disconnected successfully');
    } catch (error) {
      logError('Wallet disconnect error:', error);
    } finally {
      setIsLoading(false);
    }
  };

  // Refresh the session
  const refreshSession = async (): Promise<boolean> => {
    try {
      const isAuthenticated = await authTokens.isAuthenticated();
      if (!isAuthenticated) return false;

      const walletAddress = await authTokens.getWalletAddress();
      if (!walletAddress) return false;

      // Check session status
      const sessionValid = await walletAuthService.checkSessionStatus();

      if (sessionValid) {
        // Get updated user profile
        const userProfile = await userService.getProfile();
        setUser(userProfile);
        return true;
      } else {
        // Session expired, clear tokens
        await authTokens.clearTokens();
        setUser(null);
        return false;
      }
    } catch (error) {
      logError('Session refresh error:', error);
      return false;
    }
  };

  // Update user profile
  const updateUserProfile = async (profileData: Partial<User['profile']>): Promise<boolean> => {
    try {
      if (!user) return false;

      const updatedProfile = await userService.updateProfile(profileData);

      if (updatedProfile) {
        setUser(updatedProfile);
        return true;
      }

      return false;
    } catch (error) {
      logError('Profile update error:', error);
      Alert.alert('Error', 'Failed to update profile');
      return false;
    }
  };

  // Get wallet address
  const getWalletAddress = async (): Promise<string | null> => {
    return await authTokens.getWalletAddress();
  };

  // Initialize auth state
  useEffect(() => {
    const initializeAuth = async () => {
      try {
        setIsLoading(true);

        // Check if user has stored authentication
        const isAuthenticated = await authTokens.isAuthenticated();

        if (isAuthenticated) {
          // Try to refresh session and get user profile
          const sessionValid = await refreshSession();

          if (!sessionValid) {
            log('Session expired, user needs to re-authenticate');
          }
        }
      } catch (error) {
        logError('Auth initialization error:', error);
      } finally {
        setIsLoading(false);
      }
    };

    initializeAuth();
  }, []);

  const value: AuthContextType = {
    // State
    user,
    isAuthenticated: !!user,
    isLoading,
    isAuthenticating,
    authError,

    // Methods
    connectWallet,
    disconnectWallet,
    refreshSession,
    updateUserProfile,
    getWalletAddress,
  };

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;
};

// Create a hook to use the auth context
export const useAuth = () => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};