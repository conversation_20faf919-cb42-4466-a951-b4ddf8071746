import { io, Socket } from 'socket.io-client';
import { authTokens } from './api';
import { ENV_CONFIG, log, logError, logWarn } from '../config/environment';

// WebSocket event types
export type WebSocketEventType =
  | 'trade_update'
  | 'offer_update'
  | 'chat_message'
  | 'notification'
  | 'price_update'
  | 'user_status'
  | 'escrow_update';

export interface WebSocketEvent {
  type: WebSocketEventType;
  data: any;
  timestamp: string;
}

export interface TradeUpdateEvent {
  tradeId: string;
  status: string;
  timeline: Array<{
    status: string;
    timestamp: string;
    note?: string;
  }>;
}

export interface ChatMessageEvent {
  chatId: string;
  message: {
    _id: string;
    sender: {
      _id: string;
      username: string;
    };
    content: string;
    type: 'text' | 'image' | 'file' | 'system';
    timestamp: string;
    attachments?: Array<{
      type: string;
      url: string;
      name: string;
    }>;
  };
}

export interface NotificationEvent {
  _id: string;
  type: 'trade' | 'offer' | 'chat' | 'system' | 'security';
  title: string;
  message: string;
  data?: any;
  isRead: boolean;
  createdAt: string;
}

export interface PriceUpdateEvent {
  cryptocurrency: string;
  fiatCurrency: string;
  price: number;
  change24h: number;
  timestamp: string;
}

// WebSocket Service Class
class WebSocketService {
  private socket: Socket | null = null;
  private reconnectAttempts = 0;
  private maxReconnectAttempts = ENV_CONFIG.WEBSOCKET_RECONNECT_ATTEMPTS;
  private reconnectDelay = ENV_CONFIG.WEBSOCKET_RECONNECT_DELAY; // Start with configured delay
  private isConnecting = false;
  private eventListeners: Map<WebSocketEventType, Set<(data: any) => void>> = new Map();
  private connectionListeners: Set<(connected: boolean) => void> = new Set();

  private readonly WS_URL = ENV_CONFIG.WS_BASE_URL;

  /**
   * Connect to Socket.IO server
   */
  async connect(): Promise<void> {
    if (this.socket?.connected || this.isConnecting) {
      return;
    }

    this.isConnecting = true;

    try {
      const token = await authTokens.getAccessToken();
      const walletAddress = await authTokens.getWalletAddress();

      if (!token || !walletAddress) {
        throw new Error('No authentication token or wallet address available');
      }

      this.socket = io(this.WS_URL, {
        auth: {
          token,
          walletAddress,
          type: 'user'
        },
        transports: ['websocket', 'polling'],
        upgrade: true,
        rememberUpgrade: true,
        timeout: 20000,
        forceNew: false,
        reconnection: true,
        reconnectionAttempts: this.maxReconnectAttempts,
        reconnectionDelay: this.reconnectDelay
      });

      this.socket.on('connect', () => {
        log('Socket.IO connected');
        this.isConnecting = false;
        this.reconnectAttempts = 0;
        this.reconnectDelay = ENV_CONFIG.WEBSOCKET_RECONNECT_DELAY;
        this.notifyConnectionListeners(true);
      });

      this.socket.on('disconnect', (reason) => {
        log('Socket.IO disconnected:', reason);
        this.isConnecting = false;
        this.notifyConnectionListeners(false);
      });

      this.socket.on('connect_error', (error) => {
        logError('Socket.IO connection error:', error);
        this.isConnecting = false;
        this.notifyConnectionListeners(false);
      });

      // Handle specific events
      this.socket.on('trade_update', (data) => this.handleEvent({ type: 'trade_update', data, timestamp: new Date().toISOString() }));
      this.socket.on('offer_update', (data) => this.handleEvent({ type: 'offer_update', data, timestamp: new Date().toISOString() }));
      this.socket.on('new_message', (data) => this.handleEvent({ type: 'chat_message', data, timestamp: new Date().toISOString() }));
      this.socket.on('notification', (data) => this.handleEvent({ type: 'notification', data, timestamp: new Date().toISOString() }));
      this.socket.on('price_update', (data) => this.handleEvent({ type: 'price_update', data, timestamp: new Date().toISOString() }));
      this.socket.on('user_status', (data) => this.handleEvent({ type: 'user_status', data, timestamp: new Date().toISOString() }));
      this.socket.on('escrow_update', (data) => this.handleEvent({ type: 'escrow_update', data, timestamp: new Date().toISOString() }));

    } catch (error) {
      logError('WebSocket connection error:', error);
      this.isConnecting = false;
      throw error;
    }
  }

  /**
   * Disconnect from Socket.IO server
   */
  disconnect(): void {
    if (this.socket) {
      this.socket.disconnect();
      this.socket = null;
    }
    this.reconnectAttempts = this.maxReconnectAttempts; // Prevent reconnection
  }

  /**
   * Check if Socket.IO is connected
   */
  isConnected(): boolean {
    return this.socket?.connected || false;
  }

  /**
   * Send message through Socket.IO
   */
  send(type: string, data: any): void {
    if (!this.isConnected()) {
      logWarn('Socket.IO not connected, cannot send message');
      return;
    }

    this.socket!.emit(type, data);
  }

  /**
   * Subscribe to specific event type
   */
  on(eventType: WebSocketEventType, callback: (data: any) => void): () => void {
    if (!this.eventListeners.has(eventType)) {
      this.eventListeners.set(eventType, new Set());
    }

    this.eventListeners.get(eventType)!.add(callback);

    // Return unsubscribe function
    return () => {
      this.eventListeners.get(eventType)?.delete(callback);
    };
  }

  /**
   * Subscribe to connection status changes
   */
  onConnectionChange(callback: (connected: boolean) => void): () => void {
    this.connectionListeners.add(callback);

    // Return unsubscribe function
    return () => {
      this.connectionListeners.delete(callback);
    };
  }

  /**
   * Subscribe to trade updates
   */
  onTradeUpdate(callback: (data: TradeUpdateEvent) => void): () => void {
    return this.on('trade_update', callback);
  }

  /**
   * Subscribe to chat messages
   */
  onChatMessage(callback: (data: ChatMessageEvent) => void): () => void {
    return this.on('chat_message', callback);
  }

  /**
   * Subscribe to notifications
   */
  onNotification(callback: (data: NotificationEvent) => void): () => void {
    return this.on('notification', callback);
  }

  /**
   * Subscribe to price updates
   */
  onPriceUpdate(callback: (data: PriceUpdateEvent) => void): () => void {
    return this.on('price_update', callback);
  }

  /**
   * Join a specific room (e.g., trade room, chat room)
   */
  joinRoom(roomType: 'trade' | 'chat' | 'offers', roomId: string): void {
    if (roomType === 'trade') {
      this.send('join_trade', roomId);
    } else {
      this.send('join_room', { roomType, roomId });
    }
  }

  /**
   * Leave a specific room
   */
  leaveRoom(roomType: 'trade' | 'chat' | 'offers', roomId: string): void {
    if (roomType === 'trade') {
      this.send('leave_trade', roomId);
    } else {
      this.send('leave_room', { roomType, roomId });
    }
  }

  /**
   * Send chat message
   */
  sendChatMessage(tradeId: string, message: string, type: 'text' | 'image' | 'file' = 'text'): void {
    this.send('send_message', {
      tradeId,
      message,
      type,
    });
  }

  /**
   * Update user online status
   */
  updateUserStatus(status: 'online' | 'away' | 'offline'): void {
    this.send('user_status', { status });
  }

  /**
   * Start typing indicator
   */
  startTyping(tradeId: string): void {
    this.send('typing_start', tradeId);
  }

  /**
   * Stop typing indicator
   */
  stopTyping(tradeId: string): void {
    this.send('typing_stop', tradeId);
  }

  /**
   * Handle incoming WebSocket events
   */
  private handleEvent(event: WebSocketEvent): void {
    const listeners = this.eventListeners.get(event.type);
    if (listeners) {
      listeners.forEach(callback => {
        try {
          callback(event.data);
        } catch (error) {
          console.error(`Error in WebSocket event listener for ${event.type}:`, error);
        }
      });
    }
  }

  /**
   * Notify connection status listeners
   */
  private notifyConnectionListeners(connected: boolean): void {
    this.connectionListeners.forEach(callback => {
      try {
        callback(connected);
      } catch (error) {
        console.error('Error in connection status listener:', error);
      }
    });
  }

  // Socket.IO handles reconnection automatically, no manual reconnection needed
}

// Create and export service instance
export const websocketService = new WebSocketService();

// WebSocket hooks for React components
export const useWebSocket = () => {
  return {
    connect: () => websocketService.connect(),
    disconnect: () => websocketService.disconnect(),
    isConnected: () => websocketService.isConnected(),
    on: (eventType: WebSocketEventType, callback: (data: any) => void) =>
      websocketService.on(eventType, callback),
    onConnectionChange: (callback: (connected: boolean) => void) =>
      websocketService.onConnectionChange(callback),
    joinRoom: (roomType: 'trade' | 'chat' | 'offers', roomId: string) =>
      websocketService.joinRoom(roomType, roomId),
    leaveRoom: (roomType: 'trade' | 'chat' | 'offers', roomId: string) =>
      websocketService.leaveRoom(roomType, roomId),
    sendChatMessage: (tradeId: string, message: string, type?: 'text' | 'image' | 'file') =>
      websocketService.sendChatMessage(tradeId, message, type),
    updateUserStatus: (status: 'online' | 'away' | 'offline') =>
      websocketService.updateUserStatus(status),
    startTyping: (tradeId: string) => websocketService.startTyping(tradeId),
    stopTyping: (tradeId: string) => websocketService.stopTyping(tradeId),
  };
};

export default websocketService;
