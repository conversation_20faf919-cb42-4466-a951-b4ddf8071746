import { authTokens, API_ENDPOINTS, ApiResponse } from './api';
import { log, logError } from '../config/environment';

// Lazy import for apiClient to avoid early initialization
const getApiClient = () => {
  const { apiClient } = require('./api');
  return apiClient;
};

// Types for wallet authentication
export interface ChallengeRequest {
  walletAddress: string;
  action?: string;
}

export interface ChallengeResponse {
  message: string;
  timestamp: number;
  walletAddress: string;
  expiresIn: number;
}

export interface VerifyRequest {
  walletAddress: string;
  signature: string;
  message: string;
  timestamp: number;
}

export interface VerifyResponse {
  user: {
    _id: string;
    walletAddress: string;
    profile: {
      displayName: string;
    };
    reputation: {
      score: number;
      totalTrades: number;
    };
  };
  sessionToken: string;
  isNewUser?: boolean;
  expiresIn: number;
}

export interface WalletAuthResponse {
  user: VerifyResponse['user'];
  sessionToken: string;
}

// Wallet Authentication Service
class WalletAuthService {
  /**
   * Step 1: Request authentication challenge from backend
   */
  async requestChallenge(walletAddress: string, action: string = 'login'): Promise<ChallengeResponse> {
    try {
      const response = await getApiClient().post<{ data: ChallengeResponse }>(
        API_ENDPOINTS.WALLET_AUTH.CHALLENGE,
        { walletAddress, action }
      );

      if (response.success && response.data) {
        return response.data;
      } else {
        throw new Error(response.message || 'Failed to get challenge');
      }
    } catch (error) {
      logError('Challenge request error:', error);
      throw error;
    }
  }

  /**
   * Step 2: Verify wallet signature and complete authentication
   */
  async verifySignature(walletAddress: string, signature: string, message: string, timestamp: number): Promise<WalletAuthResponse> {
    try {
      const response = await getApiClient().post<{ data: VerifyResponse }>(
        API_ENDPOINTS.WALLET_AUTH.VERIFY,
        { walletAddress, signature, message, timestamp }
      );

      if (response.success && response.data) {
        // Store session and wallet address (no JWT tokens in wallet auth)
        await authTokens.setTokens(
          response.data.sessionToken, // Use sessionToken as access token
          '', // No refresh token needed
          walletAddress
        );

        return {
          user: response.data.user,
          sessionToken: response.data.sessionToken
        };
      } else {
        throw new Error(response.message || 'Signature verification failed');
      }
    } catch (error) {
      logError('Signature verification error:', error);
      throw error;
    }
  }

  /**
   * Complete wallet authentication flow (challenge + verify)
   */
  async authenticateWithWallet(walletAddress: string, signMessage: (message: string) => Promise<string>): Promise<WalletAuthResponse> {
    try {
      // Step 1: Get challenge
      const challenge = await this.requestChallenge(walletAddress);

      // Step 2: Sign challenge
      const signature = await signMessage(challenge.message);

      // Step 3: Verify signature
      return await this.verifySignature(walletAddress, signature, challenge.message, challenge.timestamp);
    } catch (error) {
      logError('Wallet authentication error:', error);
      throw error;
    }
  }

  /**
   * Check session status
   */
  async checkSessionStatus(): Promise<boolean> {
    try {
      const response = await getApiClient().get<{ valid: boolean }>(
        API_ENDPOINTS.WALLET_AUTH.STATUS
      );

      return response.success && response.data?.valid === true;
    } catch (error) {
      logError('Session status check error:', error);
      return false;
    }
  }

  /**
   * Logout user and clear tokens
   */
  async logout(): Promise<void> {
    try {
      // Call logout endpoint if authenticated
      const isAuth = await authTokens.isAuthenticated();
      if (isAuth) {
        await getApiClient().post(API_ENDPOINTS.WALLET_AUTH.LOGOUT);
      }

      // Clear local tokens
      await authTokens.clearTokens();

      log('User logged out successfully');
    } catch (error) {
      logError('Logout error:', error);
      // Still clear tokens even if API call fails
      await authTokens.clearTokens();
    }
  }

  /**
   * Check if user is currently authenticated
   */
  async isAuthenticated(): Promise<boolean> {
    return authTokens.isAuthenticated();
  }

  /**
   * Get current wallet address
   */
  async getCurrentWalletAddress(): Promise<string | null> {
    return authTokens.getWalletAddress();
  }

  /**
   * Generate authentication message for signing
   * This creates a standardized message that the user signs with their wallet
   */
  generateAuthMessage(walletAddress: string, timestamp: number): string {
    return `KryptoPesa Authentication\n\nWallet: ${walletAddress}\nTimestamp: ${timestamp}\n\nBy signing this message, you authenticate with KryptoPesa using your wallet.`;
  }

  /**
   * Generate current timestamp for authentication
   */
  generateTimestamp(): number {
    return Math.floor(Date.now() / 1000);
  }

  /**
   * Validate authentication message format
   */
  validateAuthMessage(message: string, walletAddress: string, timestamp: number): boolean {
    const expectedMessage = this.generateAuthMessage(walletAddress, timestamp);
    return message === expectedMessage;
  }

  /**
   * Check if timestamp is within acceptable range (5 minutes)
   */
  isTimestampValid(timestamp: number): boolean {
    const now = Math.floor(Date.now() / 1000);
    const fiveMinutes = 5 * 60;
    return Math.abs(now - timestamp) <= fiveMinutes;
  }

  /**
   * Prepare authentication data for wallet signing
   */
  prepareAuthData(walletAddress: string): {
    message: string;
    timestamp: number;
  } {
    const timestamp = this.generateTimestamp();
    const message = this.generateAuthMessage(walletAddress, timestamp);

    return { message, timestamp };
  }

  /**
   * Complete authentication after wallet signing
   */
  async completeAuthentication(
    walletAddress: string,
    signature: string,
    message: string,
    timestamp: number
  ): Promise<WalletAuthResponse> {
    // Validate inputs
    if (!this.validateAuthMessage(message, walletAddress, timestamp)) {
      throw new Error('Invalid authentication message');
    }

    if (!this.isTimestampValid(timestamp)) {
      throw new Error('Authentication timestamp expired');
    }

    // Perform authentication with the signature and message
    return this.verifySignature(walletAddress, signature, message, timestamp);
  }
}

// Create and export service instance
export const walletAuthService = new WalletAuthService();

// Helper functions for wallet integration
export const walletAuthHelpers = {
  /**
   * Format wallet address for display
   */
  formatWalletAddress(address: string): string {
    if (address.length <= 10) return address;
    return `${address.slice(0, 6)}...${address.slice(-4)}`;
  },

  /**
   * Validate Ethereum wallet address format
   */
  isValidEthereumAddress(address: string): boolean {
    return /^0x[a-fA-F0-9]{40}$/.test(address);
  },

  /**
   * Validate Bitcoin wallet address format (basic)
   */
  isValidBitcoinAddress(address: string): boolean {
    return /^[13][a-km-zA-HJ-NP-Z1-9]{25,34}$/.test(address) ||
      /^bc1[a-z0-9]{39,59}$/.test(address);
  },

  /**
   * Determine wallet type from address
   */
  getWalletType(address: string): 'ethereum' | 'bitcoin' | 'unknown' {
    if (this.isValidEthereumAddress(address)) return 'ethereum';
    if (this.isValidBitcoinAddress(address)) return 'bitcoin';
    return 'unknown';
  },

  /**
   * Generate QR code data for wallet connection
   */
  generateWalletConnectData(walletAddress: string): string {
    const timestamp = Math.floor(Date.now() / 1000);
    return JSON.stringify({
      action: 'kryptopesa_auth',
      walletAddress,
      timestamp,
      domain: 'kryptopesa.com',
    });
  },
};

export default walletAuthService;
