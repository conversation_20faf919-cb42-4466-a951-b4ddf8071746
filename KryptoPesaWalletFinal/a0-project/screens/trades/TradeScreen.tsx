import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  TextInput,
  Modal,
  Alert,
  ActivityIndicator,
  RefreshControl,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useNavigation, useRoute } from '@react-navigation/native';
import { Ionicons } from '@expo/vector-icons';
import { useTheme } from '../../context/ThemeContext';
import { log, logError, logWarn } from '../../config/environment';
import {
  tradingService,
  userService,
  websocketService,
  EAST_AFRICAN_CONFIG,
  Trade,
  eastAfricanHelpers
} from '../../services';


// Mock data for UI development - Trade Details Screen
const mockTrade = {
  id: '1',
  status: 'payment_sent', // 'pending', 'payment_sent', 'completed', 'disputed', 'cancelled'
  type: 'buy', // from the current user's perspective
  cryptoCurrency: 'BTC',
  fiatCurrency: 'USD',
  cryptoAmount: 0.0025,
  fiatAmount: 125.5,
  price: 50200.00,
  paymentMethod: 'Bank Transfer',
  paymentDetails: 'Bank: Chase\nAccount Name: John Doe\nAccount Number: XXXX-XXXX-1234\nRouting Number: *********',
  escrowReleased: false,
  createdAt: '2023-07-05T17:30:00Z',
  timeRemaining: 30, // minutes
  counterparty: {
    id: '2',
    name: 'Alice Trader',
    completedTrades: 42,
    rating: 4.8,
    avatar: null,
  },
};

const mockMessages = [
  {
    id: '1',
    senderId: '1', // current user
    content: 'Hi, I\'m interested in this trade. I\'ll make the payment shortly.',
    timestamp: '2023-07-05T17:35:00Z',
    isRead: true,
  },
  {
    id: '2',
    senderId: '2', // counterparty
    content: 'Great! Please let me know once you\'ve made the payment.',
    timestamp: '2023-07-05T17:37:00Z',
    isRead: true,
  },
  {
    id: '3',
    senderId: '1', // current user
    content: 'I\'ve just sent the payment. Please check your account.',
    timestamp: '2023-07-05T17:45:00Z',
    isRead: true,
  },
  {
    id: '4',
    senderId: '1', // current user
    content: 'Great! Please let me know once you\'ve made the payment.',
    timestamp: '2023-07-05T14:37:00Z',
    isRead: true,
  },
  {
    id: '5',
    senderId: '2', // counterparty
    content: 'I\'ve just sent the payment. Please check your account.',
    timestamp: '2023-07-05T14:45:00Z',
    isRead: true,
  },
];

export default function TradeScreen() {
  const navigation = useNavigation();
  const route = useRoute();
  const { theme } = useTheme();
  const tradeId = (route.params as any)?.tradeId;

  // State management
  const [trade, setTrade] = useState<Trade | null>(null);
  const [messages, setMessages] = useState([]);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [userProfile, setUserProfile] = useState(null);

  // UI state
  const [newMessage, setNewMessage] = useState('');
  const [sending, setSending] = useState(false);
  const [disputeModalVisible, setDisputeModalVisible] = useState(false);
  const [disputeReason, setDisputeReason] = useState('');
  const [paymentMarkedModalVisible, setPaymentMarkedModalVisible] = useState(false);
  const [updating, setUpdating] = useState(false);

  // Load trade data
  const loadTradeData = async (showLoading = true) => {
    if (!tradeId) {
      setError('Trade ID not provided');
      setLoading(false);
      return;
    }

    try {
      if (showLoading) setLoading(true);
      setError(null);

      log(`Fetching trade data for trade ID: ${tradeId}`);
      const [tradeData, profileData] = await Promise.all([
        tradingService.getTrade(tradeId),
        userService.getProfile(),
      ]);

      log('Successfully fetched trade data:', tradeData._id);
      setTrade(tradeData);
      setUserProfile(profileData);

      // Join trade room for real-time updates
      log(`Joining WebSocket room for trade: ${tradeId}`);
      websocketService.joinRoom('trade', tradeId);
    } catch (err) {
      logError('Failed to load trade data:', err);
      setError(err instanceof Error ? err.message : 'Failed to load trade data');
    } finally {
      setLoading(false);
    }
  };

  // Load data on component mount
  useEffect(() => {
    loadTradeData();

    // Set up WebSocket listeners for real-time updates
    const unsubscribeTradeUpdate = websocketService.onTradeUpdate((data) => {
      if (data.tradeId === tradeId) {
        setTrade(prev => prev ? { ...prev, status: data.status, timeline: data.timeline } : null);
      }
    });

    const unsubscribeChatMessage = websocketService.onChatMessage((data) => {
      if (data.chatId === tradeId) {
        setMessages(prev => [...prev, data.message]);
      }
    });

    return () => {
      unsubscribeTradeUpdate();
      unsubscribeChatMessage();
      websocketService.leaveRoom('trade', tradeId);
    };
  }, [tradeId]);

  // Refresh handler
  const onRefresh = async () => {
    setRefreshing(true);
    await loadTradeData(false);
    setRefreshing(false);
  };

  const sendMessage = async () => {
    if (!newMessage.trim() || !trade || sending) return;

    try {
      setSending(true);

      // Send message via WebSocket
      websocketService.sendChatMessage(tradeId, newMessage.trim());
      setNewMessage('');
    } catch (err) {
      console.error('Failed to send message:', err);
      Alert.alert('Failed to Send', 'Please try again');
    } finally {
      setSending(false);
    }
  };

  const markAsPaid = async () => {
    if (!trade || updating) return;

    try {
      setUpdating(true);

      await tradingService.updateTradeStatus(trade._id, 'payment_sent', 'Payment has been sent');

      setPaymentMarkedModalVisible(true);

      // Refresh trade data
      await loadTradeData(false);
    } catch (err) {
      console.error('Failed to mark as paid:', err);
      Alert.alert('Failed to Update', err instanceof Error ? err.message : 'Please try again');
    } finally {
      setUpdating(false);
    }
  };

  const releaseEscrow = () => {
    if (!trade || updating) return;

    // Confirm with the user
    Alert.alert(
      'Release Escrow',
      `Are you sure you want to release ${trade.cryptocurrency.amount} ${trade.cryptocurrency.type}? This action cannot be undone.`,
      [
        {
          text: 'Cancel',
          style: 'cancel',
        },
        {
          text: 'Release',
          onPress: async () => {
            try {
              setUpdating(true);

              await tradingService.updateTradeStatus(trade._id, 'completed', 'Escrow released - trade completed');

              Alert.alert(
                'Trade Completed!',
                'The escrow has been released and the trade is now complete.',
                [{ text: 'OK', onPress: () => navigation.goBack() }]
              );

              // Refresh trade data
              await loadTradeData(false);
            } catch (err) {
              console.error('Failed to release escrow:', err);
              Alert.alert('Failed to Release', err instanceof Error ? err.message : 'Please try again');
            } finally {
              setUpdating(false);
            }
          },
        },
      ],
    );
  };

  const raiseDispute = async () => {
    if (!disputeReason.trim()) {
      Alert.alert('Invalid Reason', 'Please provide a reason for the dispute.');
      return;
    }

    if (!trade || updating) return;

    try {
      setUpdating(true);

      await tradingService.updateTradeStatus(trade._id, 'disputed', disputeReason);

      setDisputeModalVisible(false);
      setDisputeReason('');

      Alert.alert(
        'Dispute Raised',
        'Your dispute has been submitted. Our support team will review the case within 24 hours.',
        [{ text: 'OK' }]
      );

      // Refresh trade data
      await loadTradeData(false);
    } catch (err) {
      console.error('Failed to raise dispute:', err);
      Alert.alert('Failed to Submit', err instanceof Error ? err.message : 'Please try again');
    } finally {
      setUpdating(false);
    }
  };

  const cancelTrade = () => {
    if (!trade || updating) return;

    // Confirm with the user
    Alert.alert(
      'Cancel Trade',
      'Are you sure you want to cancel this trade? This action cannot be undone.',
      [
        {
          text: 'No',
          style: 'cancel',
        },
        {
          text: 'Yes, Cancel',
          style: 'destructive',
          onPress: async () => {
            try {
              setUpdating(true);

              await tradingService.cancelTrade(trade._id, 'Trade cancelled by user');

              Alert.alert(
                'Trade Cancelled',
                'The trade has been cancelled successfully.',
                [{ text: 'OK', onPress: () => navigation.goBack() }]
              );

              // Refresh trade data
              await loadTradeData(false);
            } catch (err) {
              console.error('Failed to cancel trade:', err);
              Alert.alert('Failed to Cancel', err instanceof Error ? err.message : 'Please try again');
            } finally {
              setUpdating(false);
            }
          },
        },
      ],
    );
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'pending':
        return theme.colors.warning;
      case 'funded':
        return theme.colors.info;
      case 'payment_sent':
        return theme.colors.primary;
      case 'payment_confirmed':
        return theme.colors.success;
      case 'completed':
        return theme.colors.success;
      case 'disputed':
        return theme.colors.error;
      case 'cancelled':
        return theme.colors.textSecondary;
      default:
        return theme.colors.textSecondary;
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'pending':
        return 'Waiting for Escrow';
      case 'funded':
        return 'Escrow Funded';
      case 'payment_sent':
        return 'Payment Sent';
      case 'payment_confirmed':
        return 'Payment Confirmed';
      case 'completed':
        return 'Trade Completed';
      case 'disputed':
        return 'Under Dispute';
      case 'cancelled':
        return 'Trade Cancelled';
      default:
        return status.charAt(0).toUpperCase() + status.slice(1).replace('_', ' ');
    }
  };

  // Check if current user is buyer or seller
  const isCurrentUserBuyer = () => {
    return trade && userProfile && trade.buyer._id === userProfile._id;
  };

  const isCurrentUserSeller = () => {
    return trade && userProfile && trade.seller._id === userProfile._id;
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleString();
  };

  const styles = createStyles(theme);

  if (loading) {
    return (
      <SafeAreaView style={styles.container}>
        <View style={styles.header}>
          <TouchableOpacity onPress={() => navigation.goBack()}>
            <Ionicons name="arrow-back" size={24} color={theme.colors.text} />
          </TouchableOpacity>
          <Text style={styles.headerTitle}>Trade Details</Text>
          <View style={{ width: 24 }} />
        </View>
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={theme.colors.primary} />
          <Text style={styles.loadingText}>Loading trade details...</Text>
        </View>
      </SafeAreaView>
    );
  }

  if (error || !trade) {
    return (
      <SafeAreaView style={styles.container}>
        <View style={styles.header}>
          <TouchableOpacity onPress={() => navigation.goBack()}>
            <Ionicons name="arrow-back" size={24} color={theme.colors.text} />
          </TouchableOpacity>
          <Text style={styles.headerTitle}>Trade Details</Text>
          <View style={{ width: 24 }} />
        </View>
        <View style={styles.errorContainer}>
          <Ionicons name="alert-circle" size={48} color={theme.colors.error} />
          <Text style={styles.errorText}>Failed to load trade</Text>
          <Text style={styles.errorSubText}>{error || 'Trade not found'}</Text>
          <TouchableOpacity style={styles.retryButton} onPress={() => loadTradeData()}>
            <Text style={styles.retryButtonText}>Retry</Text>
          </TouchableOpacity>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.header}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => navigation.goBack()}
        >
          <Ionicons name="arrow-back" size={24} color={theme.colors.text} />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>KryptoPesa Trade</Text>
        <TouchableOpacity
          style={styles.menuButton}
          onPress={() => setDisputeModalVisible(true)}
        >
          <Ionicons name="ellipsis-vertical" size={24} color={theme.colors.text} />
        </TouchableOpacity>
      </View>

      <ScrollView
        style={styles.scrollView}
        refreshControl={
          <RefreshControl
            refreshing={refreshing}
            onRefresh={onRefresh}
            colors={[theme.colors.primary]}
          />
        }
      >
        <View style={styles.tradeCard}>
          <View style={styles.tradeHeader}>
            <View style={styles.tradeType}>
              <Text style={[
                styles.tradeTypeText,
                trade.type === 'buy' ? styles.buyText : styles.sellText
              ]}>
                {trade.type === 'buy' ? 'Buying' : 'Selling'}
              </Text>
            </View>
            <View style={[styles.statusBadge, { backgroundColor: `${getStatusColor(trade.status)}20` }]}>
              <Text style={[styles.statusText, { color: getStatusColor(trade.status) }]}>
                {getStatusText(trade.status)}
              </Text>
            </View>
          </View>

          <View style={styles.tradeDetails}>
            <View style={styles.detailRow}>
              <Text style={styles.detailLabel}>Cryptocurrency</Text>
              <View style={styles.detailValueContainer}>
                <View style={styles.cryptoIcon}>
                  <Text style={styles.cryptoIconText}>
                    {EAST_AFRICAN_CONFIG.CRYPTOCURRENCIES[trade.cryptocurrency.type]?.symbol || trade.cryptocurrency.type}
                  </Text>
                </View>
                <Text style={styles.detailValue}>
                  {trade.cryptocurrency.amount} {trade.cryptocurrency.type}
                </Text>
              </View>
            </View>

            <View style={styles.detailRow}>
              <Text style={styles.detailLabel}>Exchange Rate</Text>
              <Text style={styles.detailValue}>
                {eastAfricanHelpers.formatCurrencyAmount(trade.fiat.exchangeRate, trade.fiat.currency)} per {trade.cryptocurrency.type}
              </Text>
            </View>

            <View style={styles.detailRow}>
              <Text style={styles.detailLabel}>Total Amount</Text>
              <Text style={styles.detailValue}>
                {eastAfricanHelpers.formatCurrencyAmount(trade.fiat.amount, trade.fiat.currency)}
              </Text>
            </View>

            <View style={styles.detailRow}>
              <Text style={styles.detailLabel}>Payment Method</Text>
              <View style={styles.paymentMethodContainer}>
                <Text style={styles.paymentMethodIcon}>
                  {EAST_AFRICAN_CONFIG.PAYMENT_METHODS[trade.paymentMethod]?.icon}
                </Text>
                <Text style={styles.detailValue}>
                  {eastAfricanHelpers.getPaymentMethodName(trade.paymentMethod)}
                </Text>
              </View>
            </View>

            {trade.paymentDetails && (
              <View style={styles.paymentDetailsContainer}>
                <Text style={styles.paymentDetailsTitle}>Payment Details</Text>
                {trade.paymentDetails.phoneNumber && (
                  <Text style={styles.paymentDetailText}>
                    Phone: {trade.paymentDetails.phoneNumber}
                  </Text>
                )}
                {trade.paymentDetails.accountName && (
                  <Text style={styles.paymentDetailText}>
                    Account: {trade.paymentDetails.accountName}
                  </Text>
                )}
                {trade.paymentDetails.bankName && (
                  <Text style={styles.paymentDetailText}>
                    Bank: {trade.paymentDetails.bankName}
                  </Text>
                )}
              </View>
            )}

            <View style={styles.detailRow}>
              <Text style={styles.detailLabel}>Created</Text>
              <Text style={styles.detailValue}>{formatDate(trade.createdAt)}</Text>
            </View>

            {trade.expiresAt && new Date(trade.expiresAt) > new Date() && (
              <View style={styles.timeRemainingContainer}>
                <Ionicons name="time-outline" size={16} color={theme.colors.warning} />
                <Text style={styles.timeRemainingText}>
                  Expires: {formatDate(trade.expiresAt)}
                </Text>
              </View>
            )}
          </View>

          <View style={styles.counterpartySection}>
            <Text style={styles.sectionTitle}>
              Trading with {isCurrentUserBuyer() ? 'Seller' : 'Buyer'}
            </Text>
            <View style={styles.counterpartyInfo}>
              <View style={styles.counterpartyLeft}>
                <Ionicons name="person-circle" size={40} color={theme.colors.textSecondary} />
                <View style={styles.counterpartyDetails}>
                  <Text style={styles.counterpartyName}>
                    {isCurrentUserBuyer() ? trade.seller.username : trade.buyer.username}
                  </Text>
                  <View style={styles.counterpartyStats}>
                    <Text style={styles.tradeCount}>
                      {isCurrentUserBuyer() ? 'Seller' : 'Buyer'}
                    </Text>
                  </View>
                </View>
              </View>
              <TouchableOpacity
                style={styles.viewProfileButton}
                onPress={() => (navigation as any).navigate('TraderProfile', {
                  traderId: isCurrentUserBuyer() ? trade.seller._id : trade.buyer._id
                })}
              >
                <Text style={styles.viewProfileText}>View Profile</Text>
              </TouchableOpacity>
            </View>
          </View>

          {/* Action Buttons based on trade status and user role */}
          {isCurrentUserBuyer() && trade.status === 'funded' && (
            <View style={styles.actionSection}>
              <Text style={styles.sectionTitle}>External Payment Required</Text>
              <Text style={styles.instructionText}>
                Send {eastAfricanHelpers.formatCurrencyAmount(trade.fiat.amount, trade.fiat.currency)} to the seller using {eastAfricanHelpers.getPaymentMethodName(trade.paymentMethod)} outside this platform.
              </Text>
              <Text style={styles.warningText}>
                ⚠️ KryptoPesa does not process payments. Handle payment externally and mark as paid only after sending.
              </Text>
              <TouchableOpacity
                style={[styles.markAsPaidButton, updating && styles.buttonDisabled]}
                onPress={markAsPaid}
                disabled={updating}
              >
                {updating ? (
                  <ActivityIndicator size="small" color="#FFFFFF" />
                ) : (
                  <Text style={styles.markAsPaidButtonText}>I Have Sent Payment Externally</Text>
                )}
              </TouchableOpacity>
            </View>
          )}

          {isCurrentUserSeller() && trade.status === 'payment_sent' && (
            <View style={styles.actionSection}>
              <Text style={styles.sectionTitle}>External Payment Confirmation</Text>
              <Text style={styles.instructionText}>
                Check your {eastAfricanHelpers.getPaymentMethodName(trade.paymentMethod)} account to confirm payment receipt outside this platform.
              </Text>
              <Text style={styles.warningText}>
                ⚠️ Only release crypto after confirming payment in your external account.
              </Text>
              <TouchableOpacity
                style={[styles.releaseEscrowButton, updating && styles.buttonDisabled]}
                onPress={releaseEscrow}
                disabled={updating}
              >
                {updating ? (
                  <ActivityIndicator size="small" color="#FFFFFF" />
                ) : (
                  <Text style={styles.releaseEscrowButtonText}>Payment Received - Release {trade.cryptocurrency.type}</Text>
                )}
              </TouchableOpacity>
            </View>
          )}

          {(trade.status === 'pending' || trade.status === 'funded') && (
            <TouchableOpacity
              style={[styles.cancelButton, updating && styles.buttonDisabled]}
              onPress={cancelTrade}
              disabled={updating}
            >
              {updating ? (
                <ActivityIndicator size="small" color={theme.colors.error} />
              ) : (
                <Text style={styles.cancelButtonText}>Cancel Trade</Text>
              )}
            </TouchableOpacity>
          )}
        </View>

        <View style={styles.chatSection}>
          <View style={styles.chatHeader}>
            <Text style={styles.sectionTitle}>Trade Chat</Text>
            <TouchableOpacity
              style={styles.viewFullChatButton}
              onPress={() => (navigation as any).navigate('IndividualChat', {
                traderId: isCurrentUserBuyer() ? trade.seller._id : trade.buyer._id,
                tradeId: trade._id,
                traderName: isCurrentUserBuyer() ? trade.seller.username : trade.buyer.username
              })}
            >
              <Text style={styles.viewFullChatText}>Full Chat</Text>
              <Ionicons name="chevron-forward" size={16} color={theme.colors.primary} />
            </TouchableOpacity>
          </View>

          <View style={styles.messagesContainer}>
            {messages.length > 0 ? (
              messages.slice(-3).map((message, index) => (
                <View
                  key={message._id || index}
                  style={[
                    styles.messageItem,
                    message.sender._id === userProfile?._id ? styles.sentMessage :
                      message.type === 'system' ? styles.systemMessage : styles.receivedMessage
                  ]}
                >
                  {message.type === 'system' ? (
                    <View style={styles.systemMessageContent}>
                      <Ionicons name="information-circle" size={16} color={theme.colors.textSecondary} />
                      <Text style={styles.systemMessageText}>{message.content}</Text>
                    </View>
                  ) : (
                    <View style={styles.messageContent}>
                      <Text style={styles.messageText}>{message.content}</Text>
                      <Text style={styles.messageTime}>
                        {new Date(message.timestamp).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}
                      </Text>
                    </View>
                  )}
                </View>
              ))
            ) : (
              <View style={styles.emptyChat}>
                <Ionicons name="chatbubbles-outline" size={32} color={theme.colors.border} />
                <Text style={styles.emptyChatText}>No messages yet</Text>
                <Text style={styles.emptyChatSubText}>Start a conversation with your trading partner</Text>
              </View>
            )}
          </View>

          {trade.status !== 'completed' && trade.status !== 'cancelled' && (
            <View style={styles.messageInputContainer}>
              <TextInput
                style={styles.messageInput}
                placeholder="Type a message..."
                placeholderTextColor={theme.colors.inputPlaceholder}
                value={newMessage}
                onChangeText={setNewMessage}
                multiline
                maxLength={500}
              />
              <TouchableOpacity
                style={[styles.sendButton, sending && styles.sendButtonDisabled]}
                onPress={sendMessage}
                disabled={!newMessage.trim() || sending}
              >
                {sending ? (
                  <ActivityIndicator size="small" color={theme.colors.primary} />
                ) : (
                  <Ionicons
                    name="send"
                    size={20}
                    color={newMessage.trim() ? theme.colors.primary : theme.colors.textSecondary}
                  />
                )}
              </TouchableOpacity>
            </View>
          )}
        </View>
      </ScrollView>

      {/* Dispute Modal */}
      <Modal
        animationType="slide"
        transparent={true}
        visible={disputeModalVisible}
        onRequestClose={() => setDisputeModalVisible(false)}
      >
        <View style={styles.modalOverlay}>
          <View style={styles.modalContent}>
            <View style={styles.modalHeader}>
              <Text style={styles.modalTitle}>Raise a Dispute</Text>
              <TouchableOpacity onPress={() => setDisputeModalVisible(false)}>
                <Ionicons name="close" size={24} color={theme.colors.text} />
              </TouchableOpacity>
            </View>

            <ScrollView style={styles.modalScrollView}>
              <Text style={styles.modalText}>
                Please provide details about why you're raising a dispute. Our support team will review your case.
              </Text>

              <Text style={styles.inputLabel}>Reason for dispute</Text>
              <TextInput
                style={styles.disputeInput}
                placeholder="Explain the issue in detail..."
                placeholderTextColor={theme.colors.inputPlaceholder}
                value={disputeReason}
                onChangeText={setDisputeReason}
                multiline
                numberOfLines={5}
                textAlignVertical="top"
              />

              <View style={styles.warningContainer}>
                <Ionicons name="warning" size={20} color={theme.colors.warning} />
                <Text style={styles.warningText}>
                  Raising a dispute will pause the trade until our support team resolves the issue. Please only raise disputes for legitimate reasons.
                </Text>
              </View>
            </ScrollView>

            <View style={styles.modalFooter}>
              <TouchableOpacity
                style={styles.cancelDisputeButton}
                onPress={() => setDisputeModalVisible(false)}
              >
                <Text style={styles.cancelDisputeButtonText}>Cancel</Text>
              </TouchableOpacity>
              <TouchableOpacity
                style={[
                  styles.raiseDisputeButton,
                  !disputeReason.trim() && styles.disabledButton
                ]}
                onPress={raiseDispute}
                disabled={!disputeReason.trim()}
              >
                <Text style={styles.raiseDisputeButtonText}>Raise Dispute</Text>
              </TouchableOpacity>
            </View>
          </View>
        </View>
      </Modal>

      {/* Payment Marked Modal */}
      <Modal
        animationType="fade"
        transparent={true}
        visible={paymentMarkedModalVisible}
        onRequestClose={() => setPaymentMarkedModalVisible(false)}
      >
        <View style={styles.paymentMarkedOverlay}>
          <View style={styles.paymentMarkedContent}>
            <View style={styles.paymentMarkedIcon}>
              <Ionicons name="checkmark-circle" size={60} color={theme.colors.success} />
            </View>
            <Text style={styles.paymentMarkedTitle}>Payment Marked as Sent</Text>
            <Text style={styles.paymentMarkedText}>
              You've marked this payment as sent. The seller will be notified and should release the crypto once they confirm receipt.
            </Text>
            <TouchableOpacity
              style={styles.paymentMarkedButton}
              onPress={() => setPaymentMarkedModalVisible(false)}
            >
              <Text style={styles.paymentMarkedButtonText}>Got it</Text>
            </TouchableOpacity>
          </View>
        </View>
      </Modal>
    </SafeAreaView>
  );
}

const createStyles = (theme: any) => StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: theme.colors.background,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: theme.colors.border,
  },
  backButton: {
    padding: 4,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: theme.colors.text,
  },
  menuButton: {
    padding: 4,
  },
  scrollView: {
    flex: 1,
  },
  tradeCard: {
    backgroundColor: theme.colors.surface,
    borderRadius: 16,
    padding: 16,
    margin: 16,
    shadowColor: theme.colors.shadow,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.05,
    shadowRadius: 4,
    elevation: 2,
  },
  tradeHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  tradeType: {
    paddingHorizontal: 12,
    paddingVertical: 4,
    borderRadius: 16,
    backgroundColor: 'rgba(16, 185, 129, 0.1)',
  },
  tradeTypeText: {
    fontSize: 14,
    fontWeight: '600',
  },
  buyText: {
    color: theme.colors.success,
  },
  sellText: {
    color: theme.colors.error,
  },
  statusBadge: {
    paddingHorizontal: 12,
    paddingVertical: 4,
    borderRadius: 16,
  },
  statusText: {
    fontSize: 14,
    fontWeight: '600',
  },
  tradeDetails: {
    marginBottom: 16,
  },
  detailRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 12,
  },
  detailLabel: {
    fontSize: 14,
    color: theme.colors.textSecondary,
  },
  detailValueContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  currencyIcon: {
    width: 16,
    height: 16,
    borderRadius: 8,
    marginRight: 4,
  },
  detailValue: {
    fontSize: 14,
    fontWeight: '500',
    color: theme.colors.text,
  },
  timeRemainingContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'rgba(245, 158, 11, 0.1)',
    borderRadius: 8,
    padding: 8,
    marginTop: 8,
  },
  timeRemainingText: {
    fontSize: 14,
    color: theme.colors.warning,
    marginLeft: 8,
  },
  counterpartySection: {
    borderTopWidth: 1,
    borderTopColor: theme.colors.border,
    paddingTop: 16,
    marginBottom: 16,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: theme.colors.text,
    marginBottom: 12,
  },
  counterpartyInfo: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  counterpartyLeft: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  counterpartyDetails: {
    marginLeft: 12,
  },
  counterpartyName: {
    fontSize: 16,
    fontWeight: '500',
    color: theme.colors.text,
    marginBottom: 4,
  },
  counterpartyStats: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  tradeCount: {
    fontSize: 14,
    color: theme.colors.textSecondary,
    marginRight: 8,
  },
  ratingContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  ratingText: {
    fontSize: 14,
    fontWeight: '500',
    color: theme.colors.text,
    marginLeft: 4,
  },
  viewProfileButton: {
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: theme.colors.border,
  },
  viewProfileText: {
    fontSize: 14,
    color: theme.colors.textSecondary,
  },
  paymentSection: {
    borderTopWidth: 1,
    borderTopColor: theme.colors.border,
    paddingTop: 16,
    marginBottom: 16,
  },
  paymentDetails: {
    backgroundColor: theme.colors.surface,
    borderRadius: 8,
    padding: 12,
    marginBottom: 16,
    flexDirection: 'row',
  },
  paymentDetailsText: {
    flex: 1,
    fontSize: 14,
    color: theme.colors.textSecondary,
    lineHeight: 20,
  },
  copyButton: {
    padding: 4,
    alignSelf: 'flex-start',
  },
  markAsPaidButton: {
    backgroundColor: theme.colors.primary,
    borderRadius: 8,
    paddingVertical: 12,
    alignItems: 'center',
  },
  markAsPaidButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: 'white',
  },
  releaseEscrowButton: {
    backgroundColor: theme.colors.success,
    borderRadius: 8,
    paddingVertical: 12,
    alignItems: 'center',
  },
  releaseEscrowButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: 'white',
  },
  cancelButton: {
    borderWidth: 1,
    borderColor: theme.colors.error,
    borderRadius: 8,
    paddingVertical: 12,
    alignItems: 'center',
  },
  cancelButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: theme.colors.error,
  },
  chatSection: {
    backgroundColor: theme.colors.surface,
    borderRadius: 16,
    padding: 16,
    margin: 16,
    marginTop: 0,
    shadowColor: theme.colors.shadow,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.05,
    shadowRadius: 4,
    elevation: 2,
  },
  chatHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  viewFullChatButton: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
  },
  viewFullChatText: {
    fontSize: 14,
    fontWeight: '500',
    color: theme.colors.primary,
  },
  messagesContainer: {
    marginBottom: 16,
  },
  messageItem: {
    marginBottom: 12,
    maxWidth: '80%',
  },
  sentMessage: {
    alignSelf: 'flex-end',
  },
  receivedMessage: {
    alignSelf: 'flex-start',
  },
  systemMessage: {
    alignSelf: 'center',
    maxWidth: '90%',
  },
  messageContent: {
    backgroundColor: theme.colors.primary,
    borderRadius: 16,
    borderBottomRightRadius: 4,
    padding: 12,
  },
  systemMessageContent: {
    flexDirection: 'row',
    backgroundColor: theme.colors.surface,
    borderRadius: 16,
    padding: 12,
    alignItems: 'center',
  },
  messageText: {
    fontSize: 14,
    color: 'white',
    marginBottom: 4,
  },
  systemMessageText: {
    fontSize: 14,
    color: theme.colors.textSecondary,
    marginLeft: 8,
    flex: 1,
  },
  messageTime: {
    fontSize: 12,
    color: 'rgba(255, 255, 255, 0.8)',
    alignSelf: 'flex-end',
  },
  messageInputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: theme.colors.surface,
    borderRadius: 24,
    paddingHorizontal: 16,
    paddingVertical: 8,
  },
  messageInput: {
    flex: 1,
    fontSize: 14,
    maxHeight: 100,
    color: theme.colors.text,
  },
  sendButton: {
    padding: 8,
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'flex-end',
  },
  modalContent: {
    backgroundColor: theme.colors.surface,
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    maxHeight: '80%',
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: theme.colors.border,
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: theme.colors.text,
  },
  modalScrollView: {
    padding: 16,
  },
  modalText: {
    fontSize: 14,
    color: theme.colors.textSecondary,
    marginBottom: 16,
  },
  inputLabel: {
    fontSize: 14,
    fontWeight: '500',
    color: theme.colors.textSecondary,
    marginBottom: 8,
  },
  disputeInput: {
    backgroundColor: theme.colors.inputBackground,
    borderRadius: 8,
    padding: 12,
    fontSize: 14,
    color: theme.colors.inputText,
    height: 120,
    marginBottom: 16,
    borderWidth: 1,
    borderColor: theme.colors.inputBorder,
  },
  warningContainer: {
    flexDirection: 'row',
    backgroundColor: 'rgba(245, 158, 11, 0.1)',
    borderRadius: 8,
    padding: 12,
    marginBottom: 16,
  },
  warningText: {
    flex: 1,
    fontSize: 14,
    color: theme.colors.warning,
    marginLeft: 8,
  },
  modalFooter: {
    flexDirection: 'row',
    padding: 16,
    borderTopWidth: 1,
    borderTopColor: theme.colors.border,
  },
  cancelDisputeButton: {
    flex: 1,
    paddingVertical: 12,
    alignItems: 'center',
    borderWidth: 1,
    borderColor: theme.colors.border,
    borderRadius: 8,
    marginRight: 8,
  },
  cancelDisputeButtonText: {
    fontSize: 16,
    fontWeight: '500',
    color: theme.colors.textSecondary,
  },
  raiseDisputeButton: {
    flex: 2,
    backgroundColor: theme.colors.error,
    paddingVertical: 12,
    alignItems: 'center',
    borderRadius: 8,
  },
  disabledButton: {
    backgroundColor: theme.colors.errorLight,
  },
  raiseDisputeButtonText: {
    fontSize: 16,
    fontWeight: '500',
    color: 'white',
  },
  paymentMarkedOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  paymentMarkedContent: {
    backgroundColor: theme.colors.surface,
    borderRadius: 16,
    padding: 24,
    width: '80%',
    alignItems: 'center',
  },
  paymentMarkedIcon: {
    marginBottom: 16,
  },
  paymentMarkedTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: theme.colors.text,
    marginBottom: 8,
    textAlign: 'center',
  },
  paymentMarkedText: {
    fontSize: 14,
    color: theme.colors.textSecondary,
    textAlign: 'center',
    marginBottom: 24,
  },
  paymentMarkedButton: {
    backgroundColor: theme.colors.primary,
    borderRadius: 8,
    paddingVertical: 12,
    paddingHorizontal: 24,
  },
  paymentMarkedButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: 'white',
  },
  // New styles for East African features
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 40,
  },
  loadingText: {
    fontSize: 16,
    color: theme.colors.textSecondary,
    marginTop: 12,
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 40,
    paddingHorizontal: 20,
  },
  errorText: {
    fontSize: 18,
    color: theme.colors.error,
    marginTop: 12,
    textAlign: 'center',
  },
  errorSubText: {
    fontSize: 14,
    color: theme.colors.textSecondary,
    marginTop: 8,
    textAlign: 'center',
  },
  retryButton: {
    backgroundColor: theme.colors.primary,
    paddingHorizontal: 20,
    paddingVertical: 10,
    borderRadius: 8,
    marginTop: 16,
  },
  retryButtonText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: '600',
  },
  cryptoIcon: {
    width: 24,
    height: 24,
    borderRadius: 12,
    backgroundColor: theme.colors.primary,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 8,
  },
  cryptoIconText: {
    color: '#FFFFFF',
    fontSize: 10,
    fontWeight: 'bold',
  },
  paymentMethodContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  paymentMethodIcon: {
    fontSize: 16,
    marginRight: 8,
  },
  paymentDetailsContainer: {
    backgroundColor: theme.colors.surface,
    padding: 12,
    borderRadius: 8,
    marginTop: 8,
  },
  paymentDetailsTitle: {
    fontSize: 14,
    fontWeight: '600',
    color: theme.colors.text,
    marginBottom: 8,
  },
  paymentDetailText: {
    fontSize: 12,
    color: theme.colors.textSecondary,
    marginBottom: 4,
  },
  actionSection: {
    backgroundColor: theme.colors.surface,
    padding: 16,
    borderRadius: 12,
    marginBottom: 16,
  },
  instructionText: {
    fontSize: 14,
    color: theme.colors.textSecondary,
    marginBottom: 12,
    lineHeight: 20,
  },
  warningText: {
    fontSize: 12,
    color: theme.colors.warning,
    marginBottom: 16,
    lineHeight: 18,
    fontStyle: 'italic',
  },
  buttonDisabled: {
    opacity: 0.6,
  },
  emptyChat: {
    alignItems: 'center',
    paddingVertical: 20,
  },
  emptyChatText: {
    fontSize: 16,
    color: theme.colors.textSecondary,
    marginTop: 8,
  },
  emptyChatSubText: {
    fontSize: 12,
    color: theme.colors.textSecondary,
    marginTop: 4,
    textAlign: 'center',
  },
  sendButtonDisabled: {
    opacity: 0.6,
  },
});