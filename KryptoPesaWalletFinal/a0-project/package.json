{"name": "a0-project", "version": "1.0.0", "license": "0BSD", "private": true, "main": "index.ts", "scripts": {"start": "npx expo start", "android": "npx expo run:android", "ios": "npx expo run:ios", "web": "npx expo start --web", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "test:coverage-check": "jest --coverage --coverageThreshold='{\"global\":{\"branches\":80,\"functions\":80,\"lines\":80,\"statements\":80}}'", "test:e2e": "detox test", "test:e2e:build": "detox build", "lint": "eslint . --ext .js,.jsx,.ts,.tsx", "lint:fix": "eslint . --ext .js,.jsx,.ts,.tsx --fix"}, "dependencies": {"@expo/vector-icons": "~14.0.4", "@react-native-async-storage/async-storage": "1.23.1", "@react-native-clipboard/clipboard": "^1.16.3", "@react-navigation/native": "^6.1.9", "@react-navigation/native-stack": "^6.9.17", "expo": "^52.0.46", "react": "^18.3.1", "react-dom": "^18.3.1", "react-native": "0.76.9", "react-native-gesture-handler": "~2.20.2", "react-native-reanimated": "~3.16.1", "react-native-safe-area-context": "4.12.0", "react-native-screens": "~4.4.0", "react-native-svg": "15.8.0", "react-native-web": "~0.19.6", "socket.io-client": "^4.8.1", "sonner-native": "^0.12.0", "expo-clipboard": "~7.0.1"}, "devDependencies": {"@babel/core": "^7.20.0", "@types/react": "~18.3.12", "typescript": "^5.1.3"}}