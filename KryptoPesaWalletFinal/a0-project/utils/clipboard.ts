import { Platform } from 'react-native';
import * as ExpoClipboard from 'expo-clipboard';

// Dynamic import to handle different platforms
let Clipboard: any = null;

// Initialize clipboard based on platform and availability
const initializeClipboard = async () => {
  if (Clipboard) return Clipboard;

  try {
    // Try Expo clipboard first (most compatible with Expo projects)
    if (ExpoClipboard) {
      Clipboard = ExpoClipboard;
      return Clipboard;
    }
  } catch (error) {
    console.warn('Expo clipboard not available, trying alternatives');
  }

  try {
    // Try the new clipboard package
    const newClipboard = await import('@react-native-clipboard/clipboard');
    Clipboard = newClipboard.default;
    return Clipboard;
  } catch (error) {
    console.warn('New clipboard package not available, falling back to legacy');
    try {
      // Fallback to legacy clipboard for compatibility
      const { Clipboard: LegacyClipboard } = await import('react-native');
      Clipboard = LegacyClipboard;
      return Clipboard;
    } catch (legacyError) {
      console.error('No clipboard implementation available');
      throw new Error('Clipboard functionality not available');
    }
  }
};

/**
 * Cross-platform clipboard utility with fallback support
 */
export const clipboardUtils = {
  /**
   * Copy text to clipboard
   * @param text - Text to copy
   */
  setString: async (text: string): Promise<void> => {
    try {
      const clipboardInstance = await initializeClipboard();

      // Use appropriate method based on clipboard implementation
      if (clipboardInstance === ExpoClipboard) {
        await ExpoClipboard.setStringAsync(text);
      } else {
        await clipboardInstance.setString(text);
      }
    } catch (error) {
      console.error('Failed to copy to clipboard:', error);
      // For web platform, try using the Web API as fallback
      if (Platform.OS === 'web' && navigator.clipboard) {
        try {
          await navigator.clipboard.writeText(text);
          return;
        } catch (webError) {
          console.error('Web clipboard also failed:', webError);
        }
      }
      throw error;
    }
  },

  /**
   * Get text from clipboard
   */
  getString: async (): Promise<string> => {
    try {
      const clipboardInstance = await initializeClipboard();

      // Use appropriate method based on clipboard implementation
      if (clipboardInstance === ExpoClipboard) {
        const result = await ExpoClipboard.getStringAsync();
        return result;
      } else {
        const result = await clipboardInstance.getString();
        return result;
      }
    } catch (error) {
      console.error('Failed to get from clipboard:', error);
      // For web platform, try using the Web API as fallback
      if (Platform.OS === 'web' && navigator.clipboard) {
        try {
          const result = await navigator.clipboard.readText();
          return result;
        } catch (webError) {
          console.error('Web clipboard also failed:', webError);
        }
      }
      throw error;
    }
  }
};
