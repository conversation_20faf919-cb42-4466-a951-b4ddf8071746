import AsyncStorage from '@react-native-async-storage/async-storage';
// Import ethers conditionally to avoid web issues
let ethers: any = null;
try {
  ethers = require('ethers');
} catch (error) {
  console.warn('Ethers not available, using fallback crypto functions');
}

/**
 * Mobile Wallet Utilities for KryptoPesa
 * Handles wallet creation, signing, and key management for React Native
 */

export interface WalletAddresses {
  ethereum: string;
  bitcoin: string;
  polygon: string;
}

export interface WalletKeyPair {
  address: string;
  privateKey: string;
  publicKey: string;
}

export class MobileWalletUtils {

  /**
   * Generate a secure mnemonic phrase using ethers or fallback
   */
  static generateMnemonic(): string[] {
    if (ethers && ethers.utils) {
      try {
        const mnemonic = ethers.utils.entropyToMnemonic(ethers.utils.randomBytes(32)); // 24 words
        return mnemonic.split(' ');
      } catch (error) {
        console.warn('Ethers mnemonic generation failed, using fallback');
      }
    }

    // Fallback: Generate a deterministic mnemonic for demo purposes
    return this.generateFallbackMnemonic();
  }

  /**
   * Generate a fallback mnemonic for demo/development purposes
   */
  private static generateFallbackMnemonic(): string[] {
    const words = [
      'abandon', 'ability', 'able', 'about', 'above', 'absent', 'absorb', 'abstract',
      'absurd', 'abuse', 'access', 'accident', 'account', 'accuse', 'achieve', 'acid',
      'acoustic', 'acquire', 'across', 'act', 'action', 'actor', 'actress', 'actual',
      'adapt', 'add', 'addict', 'address', 'adjust', 'admit', 'adult', 'advance'
    ];

    // Generate a deterministic but unique mnemonic based on timestamp
    const timestamp = Date.now();
    const mnemonic: string[] = [];

    for (let i = 0; i < 24; i++) {
      const index = (timestamp + i * 7) % words.length;
      mnemonic.push(words[index]);
    }

    return mnemonic;
  }

  /**
   * Validate mnemonic phrase
   */
  static validateMnemonic(mnemonic: string[]): boolean {
    if (ethers && ethers.utils) {
      try {
        const mnemonicString = mnemonic.join(' ');
        ethers.utils.mnemonicToSeed(mnemonicString);
        return true;
      } catch (error) {
        return false;
      }
    }

    // Fallback validation: check if it's 12 or 24 words and each word is valid
    if (mnemonic.length !== 12 && mnemonic.length !== 24) {
      return false;
    }

    // Check that each word is a non-empty string
    return mnemonic.every(word => typeof word === 'string' && word.length > 0);
  }

  /**
   * Generate wallet addresses from mnemonic
   */
  static generateWalletAddresses(mnemonic: string[]): WalletAddresses {
    try {
      const mnemonicString = mnemonic.join(' ');

      if (ethers && ethers.Wallet) {
        try {
          // Generate Ethereum wallet using ethers
          const ethWallet = ethers.Wallet.fromMnemonic(mnemonicString);

          // For now, use the same address for Polygon (same derivation path)
          const polygonAddress = ethWallet.address;

          // For Bitcoin, we'll generate a mock address
          const bitcoinAddress = this.generateBitcoinAddress(mnemonicString);

          return {
            ethereum: ethWallet.address,
            bitcoin: bitcoinAddress,
            polygon: polygonAddress,
          };
        } catch (error) {
          console.warn('Ethers wallet generation failed, using fallback');
        }
      }

      // Fallback: Generate deterministic addresses for demo
      return this.generateFallbackAddresses(mnemonicString);
    } catch (error) {
      console.error('Error generating wallet addresses:', error);
      throw new Error('Failed to generate wallet addresses');
    }
  }

  /**
   * Generate fallback addresses for demo purposes
   */
  private static generateFallbackAddresses(mnemonicString: string): WalletAddresses {
    // Simple hash function for demo
    const hash = this.simpleHash(mnemonicString);

    return {
      ethereum: `0x${hash.slice(0, 40)}`,
      bitcoin: `1${hash.slice(0, 33)}`,
      polygon: `0x${hash.slice(10, 50)}`,
    };
  }

  /**
   * Simple hash function for fallback
   */
  private static simpleHash(input: string): string {
    let hash = 0;
    for (let i = 0; i < input.length; i++) {
      const char = input.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash; // Convert to 32-bit integer
    }
    return Math.abs(hash).toString(16).padStart(50, '0');
  }

  /**
   * Get private key for Ethereum address from mnemonic
   */
  static getEthereumPrivateKey(mnemonic: string[]): string {
    try {
      const mnemonicString = mnemonic.join(' ');
      const wallet = ethers.Wallet.fromMnemonic(mnemonicString);
      return wallet.privateKey;
    } catch (error) {
      console.error('Error getting private key:', error);
      throw new Error('Failed to get private key');
    }
  }

  /**
   * Sign message with wallet private key
   */
  static async signMessage(mnemonic: string[], message: string): Promise<string> {
    try {
      const mnemonicString = mnemonic.join(' ');

      if (ethers && ethers.Wallet) {
        try {
          const wallet = ethers.Wallet.fromMnemonic(mnemonicString);
          return await wallet.signMessage(message);
        } catch (error) {
          console.warn('Ethers signing failed, using fallback');
        }
      }

      // Fallback: Generate a deterministic signature for demo
      return this.generateFallbackSignature(mnemonicString, message);
    } catch (error) {
      console.error('Error signing message:', error);
      throw new Error('Failed to sign message');
    }
  }

  /**
   * Generate fallback signature for demo purposes
   */
  private static generateFallbackSignature(mnemonic: string, message: string): string {
    const combined = mnemonic + message;
    const hash = this.simpleHash(combined);
    return `0x${hash}${hash.slice(0, 30)}`; // Mock signature format
  }

  /**
   * Verify message signature
   */
  static verifySignature(message: string, signature: string, address: string): boolean {
    if (ethers && ethers.utils) {
      try {
        const recoveredAddress = ethers.utils.verifyMessage(message, signature);
        return recoveredAddress.toLowerCase() === address.toLowerCase();
      } catch (error) {
        console.warn('Ethers verification failed, using fallback');
      }
    }

    // Fallback: Simple signature verification for demo
    // In a real app, this would need proper cryptographic verification
    return signature.length > 10 && address.length > 10;
  }

  /**
   * Generate Bitcoin address (simplified for demo)
   * In production, use proper Bitcoin libraries
   */
  private static generateBitcoinAddress(mnemonicString: string): string {
    // This is a simplified Bitcoin address generation
    // In production, use proper Bitcoin libraries like bitcoinjs-lib
    const hash = ethers.utils.keccak256(ethers.utils.toUtf8Bytes(mnemonicString));
    const addressBytes = hash.slice(2, 42); // Take 20 bytes
    return `1${addressBytes.slice(0, 33)}`; // Mock Bitcoin address format
  }

  /**
   * Encrypt mnemonic for secure storage
   */
  static encryptMnemonic(mnemonic: string[], password: string): string {
    // Simple encryption for demo - use proper encryption in production
    const mnemonicString = mnemonic.join(' ');
    const encrypted = Buffer.from(mnemonicString + ':' + password).toString('base64');
    return encrypted;
  }

  /**
   * Decrypt mnemonic from storage
   */
  static decryptMnemonic(encryptedMnemonic: string, password: string): string[] {
    try {
      // Simple decryption for demo - use proper decryption in production
      const decrypted = Buffer.from(encryptedMnemonic, 'base64').toString('utf8');
      const parts = decrypted.split(':');

      if (parts.length !== 2) {
        throw new Error('Invalid encrypted format');
      }

      const [mnemonicString, storedPassword] = parts;

      // Verify password matches
      if (storedPassword !== password) {
        throw new Error('Invalid password');
      }

      return mnemonicString.split(' ');
    } catch (error) {
      console.error('Error decrypting mnemonic:', error);
      throw new Error('Failed to decrypt mnemonic');
    }
  }

  /**
   * Hash PIN for secure storage
   */
  static hashPin(pin: string): string {
    if (ethers && ethers.utils) {
      try {
        return ethers.utils.keccak256(ethers.utils.toUtf8Bytes(pin));
      } catch (error) {
        console.warn('Ethers PIN hashing failed, using fallback');
      }
    }

    // Fallback: Simple hash for demo
    return this.simpleHash(pin + 'salt');
  }

  /**
   * Verify PIN hash
   */
  static verifyPin(pin: string, hash: string): boolean {
    return this.hashPin(pin) === hash;
  }
}

// Export default instance
export const mobileWalletUtils = MobileWalletUtils;
