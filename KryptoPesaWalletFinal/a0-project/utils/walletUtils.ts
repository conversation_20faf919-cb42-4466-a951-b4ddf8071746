import AsyncStorage from '@react-native-async-storage/async-storage';
import { ethers } from 'ethers';

/**
 * Mobile Wallet Utilities for KryptoPesa
 * Handles wallet creation, signing, and key management for React Native
 */

export interface WalletAddresses {
  ethereum: string;
  bitcoin: string;
  polygon: string;
}

export interface WalletKeyPair {
  address: string;
  privateKey: string;
  publicKey: string;
}

export class MobileWalletUtils {
  
  /**
   * Generate a secure mnemonic phrase using ethers
   */
  static generateMnemonic(): string[] {
    const mnemonic = ethers.utils.entropyToMnemonic(ethers.utils.randomBytes(32)); // 24 words
    return mnemonic.split(' ');
  }

  /**
   * Validate mnemonic phrase
   */
  static validateMnemonic(mnemonic: string[]): boolean {
    try {
      const mnemonicString = mnemonic.join(' ');
      ethers.utils.mnemonicToSeed(mnemonicString);
      return true;
    } catch (error) {
      return false;
    }
  }

  /**
   * Generate wallet addresses from mnemonic
   */
  static generateWalletAddresses(mnemonic: string[]): WalletAddresses {
    try {
      const mnemonicString = mnemonic.join(' ');
      
      // Generate Ethereum wallet
      const ethWallet = ethers.Wallet.fromMnemonic(mnemonicString);
      
      // For now, use the same address for Polygon (same derivation path)
      // In production, you might want different derivation paths
      const polygonAddress = ethWallet.address;
      
      // For Bitcoin, we'll generate a mock address for now
      // In production, use proper Bitcoin libraries
      const bitcoinAddress = this.generateBitcoinAddress(mnemonicString);

      return {
        ethereum: ethWallet.address,
        bitcoin: bitcoinAddress,
        polygon: polygonAddress,
      };
    } catch (error) {
      console.error('Error generating wallet addresses:', error);
      throw new Error('Failed to generate wallet addresses');
    }
  }

  /**
   * Get private key for Ethereum address from mnemonic
   */
  static getEthereumPrivateKey(mnemonic: string[]): string {
    try {
      const mnemonicString = mnemonic.join(' ');
      const wallet = ethers.Wallet.fromMnemonic(mnemonicString);
      return wallet.privateKey;
    } catch (error) {
      console.error('Error getting private key:', error);
      throw new Error('Failed to get private key');
    }
  }

  /**
   * Sign message with wallet private key
   */
  static async signMessage(mnemonic: string[], message: string): Promise<string> {
    try {
      const mnemonicString = mnemonic.join(' ');
      const wallet = ethers.Wallet.fromMnemonic(mnemonicString);
      return await wallet.signMessage(message);
    } catch (error) {
      console.error('Error signing message:', error);
      throw new Error('Failed to sign message');
    }
  }

  /**
   * Verify message signature
   */
  static verifySignature(message: string, signature: string, address: string): boolean {
    try {
      const recoveredAddress = ethers.utils.verifyMessage(message, signature);
      return recoveredAddress.toLowerCase() === address.toLowerCase();
    } catch (error) {
      console.error('Error verifying signature:', error);
      return false;
    }
  }

  /**
   * Generate Bitcoin address (simplified for demo)
   * In production, use proper Bitcoin libraries
   */
  private static generateBitcoinAddress(mnemonicString: string): string {
    // This is a simplified Bitcoin address generation
    // In production, use proper Bitcoin libraries like bitcoinjs-lib
    const hash = ethers.utils.keccak256(ethers.utils.toUtf8Bytes(mnemonicString));
    const addressBytes = hash.slice(2, 42); // Take 20 bytes
    return `1${addressBytes.slice(0, 33)}`; // Mock Bitcoin address format
  }

  /**
   * Encrypt mnemonic for secure storage
   */
  static encryptMnemonic(mnemonic: string[], password: string): string {
    // Simple encryption for demo - use proper encryption in production
    const mnemonicString = mnemonic.join(' ');
    const encrypted = Buffer.from(mnemonicString + ':' + password).toString('base64');
    return encrypted;
  }

  /**
   * Decrypt mnemonic from storage
   */
  static decryptMnemonic(encryptedMnemonic: string, password: string): string[] {
    try {
      // Simple decryption for demo - use proper decryption in production
      const decrypted = Buffer.from(encryptedMnemonic, 'base64').toString('utf8');
      const [mnemonicString] = decrypted.split(':');
      return mnemonicString.split(' ');
    } catch (error) {
      console.error('Error decrypting mnemonic:', error);
      throw new Error('Failed to decrypt mnemonic');
    }
  }

  /**
   * Hash PIN for secure storage
   */
  static hashPin(pin: string): string {
    return ethers.utils.keccak256(ethers.utils.toUtf8Bytes(pin));
  }

  /**
   * Verify PIN hash
   */
  static verifyPin(pin: string, hash: string): boolean {
    return this.hashPin(pin) === hash;
  }
}

// Export default instance
export const mobileWalletUtils = MobileWalletUtils;
