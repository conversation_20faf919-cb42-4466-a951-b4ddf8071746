import React from 'react';
import { render, act, waitFor } from '@testing-library/react-native';
import { WalletAuthProvider, useWalletAuth } from '../../context/WalletAuthContext';

// Test component to access context
const TestComponent = () => {
    const { wallet, isAuthenticated, login, logout } = useWalletAuth();

    return (
        <>
            {isAuthenticated ? <div testID="authenticated">Authenticated</div> : <div testID="not-authenticated">Not Authenticated</div>}
            {wallet && <div testID="wallet-address">{wallet.address}</div>}
        </>
    );
};

describe('WalletAuthContext', () => {
    beforeEach(() => {
        jest.clearAllMocks();
    });

    it('should provide initial state', () => {
        const { getByTestId } = render(
            <WalletAuthProvider>
                <TestComponent />
            </WalletAuthProvider>
        );

        expect(getByTestId('not-authenticated')).toBeTruthy();
    });

    it('should authenticate user with valid wallet', async () => {
        const mockWallet = {
            address: '******************************************',
            mnemonic: 'test mnemonic phrase',
            privateKey: '******************************************123456789012345678901234',
        };

        const TestComponentWithLogin = () => {
            const { wallet, isAuthenticated, login } = useWalletAuth();

            React.useEffect(() => {
                login(mockWallet);
            }, []);

            return (
                <>
                    {isAuthenticated ? <div testID="authenticated">Authenticated</div> : <div testID="not-authenticated">Not Authenticated</div>}
                    {wallet && <div testID="wallet-address">{wallet.address}</div>}
                </>
            );
        };

        const { getByTestId } = render(
            <WalletAuthProvider>
                <TestComponentWithLogin />
            </WalletAuthProvider>
        );

        await waitFor(() => {
            expect(getByTestId('authenticated')).toBeTruthy();
            expect(getByTestId('wallet-address')).toBeTruthy();
        });
    });

    it('should logout user', async () => {
        const mockWallet = {
            address: '******************************************',
            mnemonic: 'test mnemonic phrase',
            privateKey: '******************************************123456789012345678901234',
        };

        const TestComponentWithLogout = () => {
            const { wallet, isAuthenticated, login, logout } = useWalletAuth();
            const [hasLoggedIn, setHasLoggedIn] = React.useState(false);

            React.useEffect(() => {
                if (!hasLoggedIn) {
                    login(mockWallet);
                    setHasLoggedIn(true);
                    setTimeout(() => logout(), 100);
                }
            }, [hasLoggedIn]);

            return (
                <>
                    {isAuthenticated ? <div testID="authenticated">Authenticated</div> : <div testID="not-authenticated">Not Authenticated</div>}
                    {wallet && <div testID="wallet-address">{wallet.address}</div>}
                </>
            );
        };

        const { getByTestId } = render(
            <WalletAuthProvider>
                <TestComponentWithLogout />
            </WalletAuthProvider>
        );

        await waitFor(() => {
            expect(getByTestId('not-authenticated')).toBeTruthy();
        });
    });

    it('should handle wallet balance updates', async () => {
        const mockWallet = {
            address: '******************************************',
            mnemonic: 'test mnemonic phrase',
            privateKey: '******************************************123456789012345678901234',
        };

        const TestComponentWithBalance = () => {
            const { wallet, updateBalance } = useWalletAuth();

            React.useEffect(() => {
                updateBalance('100.5');
            }, []);

            return (
                <>
                    {wallet?.balance && <div testID="wallet-balance">{wallet.balance}</div>}
                </>
            );
        };

        const { getByTestId } = render(
            <WalletAuthProvider>
                <TestComponentWithBalance />
            </WalletAuthProvider>
        );

        await waitFor(() => {
            expect(getByTestId('wallet-balance')).toBeTruthy();
        });
    });

    it('should persist authentication state', async () => {
        // Mock AsyncStorage
        const mockAsyncStorage = {
            getItem: jest.fn().mockResolvedValue(JSON.stringify({
                address: '******************************************',
                mnemonic: 'test mnemonic phrase',
                privateKey: '******************************************123456789012345678901234',
            })),
            setItem: jest.fn(),
            removeItem: jest.fn(),
        };

        jest.doMock('@react-native-async-storage/async-storage', () => mockAsyncStorage);

        const { getByTestId } = render(
            <WalletAuthProvider>
                <TestComponent />
            </WalletAuthProvider>
        );

        await waitFor(() => {
            expect(mockAsyncStorage.getItem).toHaveBeenCalledWith('wallet_data');
        });
    });

    it('should handle authentication errors', async () => {
        const TestComponentWithError = () => {
            const { error, login } = useWalletAuth();

            React.useEffect(() => {
                // Try to login with invalid wallet
                login(null as any);
            }, []);

            return (
                <>
                    {error && <div testID="auth-error">{error}</div>}
                </>
            );
        };

        const { getByTestId } = render(
            <WalletAuthProvider>
                <TestComponentWithError />
            </WalletAuthProvider>
        );

        await waitFor(() => {
            expect(getByTestId('auth-error')).toBeTruthy();
        });
    });
});