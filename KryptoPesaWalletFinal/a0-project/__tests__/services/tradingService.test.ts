import { tradingService } from '../../services/tradingService';

// Mock fetch
global.fetch = jest.fn();

describe('TradingService', () => {
    beforeEach(() => {
        jest.clearAllMocks();
    });

    describe('createOffer', () => {
        it('should create a new offer successfully', async () => {
            const mockOffer = {
                id: '123',
                type: 'buy',
                cryptocurrency: 'USDT',
                fiatCurrency: 'KES',
                amount: 100,
                rate: 150,
                paymentMethods: ['M-Pesa'],
                terms: 'Test terms',
            };

            (global.fetch as jest.Mock).mockResolvedValue({
                ok: true,
                json: () => Promise.resolve({ success: true, offer: mockOffer }),
            });

            const result = await tradingService.createOffer({
                type: 'buy',
                cryptocurrency: 'USDT',
                fiatCurrency: 'KES',
                amount: 100,
                rate: 150,
                paymentMethods: ['M-Pesa'],
                terms: 'Test terms',
            });

            expect(result).toEqual(mockOffer);
            expect(global.fetch).toHaveBeenCalledWith(
                expect.stringContaining('/api/offers'),
                expect.objectContaining({
                    method: 'POST',
                    headers: expect.objectContaining({
                        'Content-Type': 'application/json',
                    }),
                })
            );
        });

        it('should validate offer parameters', async () => {
            await expect(tradingService.createOffer({
                type: 'invalid' as any,
                cryptocurrency: 'USDT',
                fiatCurrency: 'KES',
                amount: 100,
                rate: 150,
                paymentMethods: ['M-Pesa'],
                terms: 'Test terms',
            })).rejects.toThrow('Invalid offer type');

            await expect(tradingService.createOffer({
                type: 'buy',
                cryptocurrency: 'USDT',
                fiatCurrency: 'KES',
                amount: -100,
                rate: 150,
                paymentMethods: ['M-Pesa'],
                terms: 'Test terms',
            })).rejects.toThrow('Amount must be positive');
        });
    });

    describe('getOffers', () => {
        it('should fetch offers with filters', async () => {
            const mockOffers = [
                {
                    id: '1',
                    type: 'buy',
                    cryptocurrency: 'USDT',
                    fiatCurrency: 'KES',
                    amount: 100,
                    rate: 150,
                },
                {
                    id: '2',
                    type: 'sell',
                    cryptocurrency: 'USDT',
                    fiatCurrency: 'KES',
                    amount: 200,
                    rate: 148,
                },
            ];

            (global.fetch as jest.Mock).mockResolvedValue({
                ok: true,
                json: () => Promise.resolve({ offers: mockOffers }),
            });

            const offers = await tradingService.getOffers({
                type: 'buy',
                cryptocurrency: 'USDT',
                fiatCurrency: 'KES',
            });

            expect(offers).toEqual(mockOffers);
            expect(global.fetch).toHaveBeenCalledWith(
                expect.stringContaining('/api/offers?type=buy&cryptocurrency=USDT&fiatCurrency=KES'),
                expect.objectContaining({ method: 'GET' })
            );
        });

        it('should handle empty results', async () => {
            (global.fetch as jest.Mock).mockResolvedValue({
                ok: true,
                json: () => Promise.resolve({ offers: [] }),
            });

            const offers = await tradingService.getOffers({});
            expect(offers).toEqual([]);
        });
    });

    describe('initiateTrade', () => {
        it('should initiate trade successfully', async () => {
            const mockTrade = {
                id: 'trade-123',
                offerId: 'offer-123',
                buyerId: 'buyer-123',
                sellerId: 'seller-123',
                amount: 100,
                status: 'initiated',
                escrowAddress: '******************************************',
            };

            (global.fetch as jest.Mock).mockResolvedValue({
                ok: true,
                json: () => Promise.resolve({ success: true, trade: mockTrade }),
            });

            const trade = await tradingService.initiateTrade('offer-123', 100);

            expect(trade).toEqual(mockTrade);
            expect(global.fetch).toHaveBeenCalledWith(
                expect.stringContaining('/api/trades'),
                expect.objectContaining({
                    method: 'POST',
                    body: JSON.stringify({
                        offerId: 'offer-123',
                        amount: 100,
                    }),
                })
            );
        });

        it('should validate trade parameters', async () => {
            await expect(tradingService.initiateTrade('', 100))
                .rejects.toThrow('Offer ID is required');

            await expect(tradingService.initiateTrade('offer-123', 0))
                .rejects.toThrow('Amount must be positive');
        });
    });

    describe('confirmPayment', () => {
        it('should confirm payment successfully', async () => {
            (global.fetch as jest.Mock).mockResolvedValue({
                ok: true,
                json: () => Promise.resolve({ success: true }),
            });

            await tradingService.confirmPayment('trade-123', 'payment-proof.jpg');

            expect(global.fetch).toHaveBeenCalledWith(
                expect.stringContaining('/api/trades/trade-123/confirm-payment'),
                expect.objectContaining({
                    method: 'POST',
                    body: JSON.stringify({
                        paymentProof: 'payment-proof.jpg',
                    }),
                })
            );
        });
    });

    describe('releaseFunds', () => {
        it('should release funds successfully', async () => {
            (global.fetch as jest.Mock).mockResolvedValue({
                ok: true,
                json: () => Promise.resolve({ success: true, txHash: '0xabcdef' }),
            });

            const txHash = await tradingService.releaseFunds('trade-123');

            expect(txHash).toBe('0xabcdef');
            expect(global.fetch).toHaveBeenCalledWith(
                expect.stringContaining('/api/trades/trade-123/release'),
                expect.objectContaining({ method: 'POST' })
            );
        });
    });
});