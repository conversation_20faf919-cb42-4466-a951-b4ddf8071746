import { walletService } from '../../services/walletService';

// Mock AsyncStorage
const mockAsyncStorage = {
    getItem: jest.fn(),
    setItem: jest.fn(),
    removeItem: jest.fn(),
};

jest.mock('@react-native-async-storage/async-storage', () => mockAsyncStorage);

describe('WalletService', () => {
    beforeEach(() => {
        jest.clearAllMocks();
    });

    describe('generateWallet', () => {
        it('should generate a new wallet with mnemonic', async () => {
            const wallet = await walletService.generateWallet();

            expect(wallet).toHaveProperty('address');
            expect(wallet).toHaveProperty('mnemonic');
            expect(wallet).toHaveProperty('privateKey');
            expect(wallet.address).toMatch(/^0x[a-fA-F0-9]{40}$/);
            expect(wallet.mnemonic.split(' ')).toHaveLength(12);
        });

        it('should generate unique wallets', async () => {
            const wallet1 = await walletService.generateWallet();
            const wallet2 = await walletService.generateWallet();

            expect(wallet1.address).not.toBe(wallet2.address);
            expect(wallet1.mnemonic).not.toBe(wallet2.mnemonic);
            expect(wallet1.privateKey).not.toBe(wallet2.privateKey);
        });
    });

    describe('importWallet', () => {
        it('should import wallet from valid mnemonic', async () => {
            const mnemonic = 'abandon abandon abandon abandon abandon abandon abandon abandon abandon abandon abandon about';
            const wallet = await walletService.importWallet(mnemonic);

            expect(wallet).toHaveProperty('address');
            expect(wallet).toHaveProperty('mnemonic', mnemonic);
            expect(wallet).toHaveProperty('privateKey');
            expect(wallet.address).toMatch(/^0x[a-fA-F0-9]{40}$/);
        });

        it('should throw error for invalid mnemonic', async () => {
            const invalidMnemonic = 'invalid mnemonic phrase';

            await expect(walletService.importWallet(invalidMnemonic))
                .rejects.toThrow('Invalid mnemonic phrase');
        });

        it('should import wallet from valid private key', async () => {
            const privateKey = '******************************************123456789012345678901234';
            const wallet = await walletService.importWalletFromPrivateKey(privateKey);

            expect(wallet).toHaveProperty('address');
            expect(wallet).toHaveProperty('privateKey', privateKey);
            expect(wallet.address).toMatch(/^0x[a-fA-F0-9]{40}$/);
        });
    });

    describe('getBalance', () => {
        it('should return balance for valid address', async () => {
            global.fetch = jest.fn().mockResolvedValue({
                ok: true,
                json: () => Promise.resolve({ result: '0x1bc16d674ec80000' }), // 2 ETH in wei
            });

            const balance = await walletService.getBalance('******************************************');

            expect(balance).toBe('2.0');
            expect(global.fetch).toHaveBeenCalledWith(
                expect.stringContaining('eth_getBalance'),
                expect.any(Object)
            );
        });

        it('should handle network errors', async () => {
            global.fetch = jest.fn().mockRejectedValue(new Error('Network error'));

            await expect(walletService.getBalance('******************************************'))
                .rejects.toThrow('Failed to fetch balance');
        });
    });

    describe('sendTransaction', () => {
        it('should send transaction successfully', async () => {
            global.fetch = jest.fn().mockResolvedValue({
                ok: true,
                json: () => Promise.resolve({ result: '0x1234567890abcdef' }),
            });

            const txHash = await walletService.sendTransaction({
                to: '******************************************',
                value: '1.0',
                privateKey: '******************************************123456789012345678901234',
            });

            expect(txHash).toBe('0x1234567890abcdef');
            expect(global.fetch).toHaveBeenCalledWith(
                expect.stringContaining('eth_sendRawTransaction'),
                expect.any(Object)
            );
        });

        it('should validate transaction parameters', async () => {
            await expect(walletService.sendTransaction({
                to: 'invalid-address',
                value: '1.0',
                privateKey: '******************************************123456789012345678901234',
            })).rejects.toThrow('Invalid recipient address');

            await expect(walletService.sendTransaction({
                to: '******************************************',
                value: '-1.0',
                privateKey: '******************************************123456789012345678901234',
            })).rejects.toThrow('Invalid amount');
        });
    });

    describe('storage operations', () => {
        it('should save wallet securely', async () => {
            const wallet = {
                address: '******************************************',
                mnemonic: 'test mnemonic phrase',
                privateKey: '******************************************123456789012345678901234',
            };

            await walletService.saveWallet(wallet);

            expect(mockAsyncStorage.setItem).toHaveBeenCalledWith(
                'wallet_encrypted',
                expect.any(String)
            );
        });

        it('should load wallet from storage', async () => {
            const encryptedWallet = 'encrypted_wallet_data';
            mockAsyncStorage.getItem.mockResolvedValue(encryptedWallet);

            const wallet = await walletService.loadWallet();

            expect(mockAsyncStorage.getItem).toHaveBeenCalledWith('wallet_encrypted');
            expect(wallet).toBeDefined();
        });

        it('should return null when no wallet in storage', async () => {
            mockAsyncStorage.getItem.mockResolvedValue(null);

            const wallet = await walletService.loadWallet();

            expect(wallet).toBeNull();
        });
    });

    describe('transaction history', () => {
        it('should fetch transaction history', async () => {
            const mockTransactions = [
                {
                    hash: '0x1234567890abcdef',
                    from: '******************************************',
                    to: '******************************************',
                    value: '1000000000000000000',
                    timestamp: Date.now(),
                },
            ];

            global.fetch = jest.fn().mockResolvedValue({
                ok: true,
                json: () => Promise.resolve({ result: mockTransactions }),
            });

            const history = await walletService.getTransactionHistory('******************************************');

            expect(history).toHaveLength(1);
            expect(history[0]).toHaveProperty('hash');
            expect(history[0]).toHaveProperty('value');
        });
    });
});