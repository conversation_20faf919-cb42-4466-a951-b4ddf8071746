/**
 * End-to-End Test: Complete Wallet Flow
 * Tests the entire user journey from wallet creation to trading
 */

import { by, device, element, expect, waitFor } from 'detox';

describe('Complete Wallet Flow E2E', () => {
    beforeAll(async () => {
        await device.launchApp();
    });

    beforeEach(async () => {
        await device.reloadReactNative();
    });

    afterAll(async () => {
        await device.terminateApp();
    });

    describe('Wallet Setup Flow', () => {
        it('should complete wallet creation flow', async () => {
            // Welcome screen
            await expect(element(by.id('welcome-screen'))).toBeVisible();
            await element(by.id('create-wallet-button')).tap();

            // Wallet creation screen
            await expect(element(by.id('wallet-creation-screen'))).toBeVisible();
            await element(by.id('generate-wallet-button')).tap();

            // Mnemonic display screen
            await waitFor(element(by.id('mnemonic-display-screen')))
                .toBeVisible()
                .withTimeout(5000);

            // Verify mnemonic is displayed
            await expect(element(by.id('mnemonic-words'))).toBeVisible();
            await element(by.id('mnemonic-copied-button')).tap();
            await element(by.id('continue-button')).tap();

            // Mnemonic verification screen
            await expect(element(by.id('mnemonic-verification-screen'))).toBeVisible();

            // Simulate mnemonic verification (simplified for testing)
            await element(by.id('word-input-0')).typeText('abandon');
            await element(by.id('word-input-1')).typeText('abandon');
            await element(by.id('word-input-2')).typeText('abandon');
            await element(by.id('verify-mnemonic-button')).tap();

            // PIN setup screen
            await expect(element(by.id('pin-setup-screen'))).toBeVisible();
            await element(by.id('pin-input')).typeText('123456');
            await element(by.id('confirm-pin-input')).typeText('123456');
            await element(by.id('set-pin-button')).tap();

            // Should navigate to main dashboard
            await waitFor(element(by.id('main-dashboard')))
                .toBeVisible()
                .withTimeout(5000);
        });

        it('should complete wallet import flow', async () => {
            // Welcome screen
            await expect(element(by.id('welcome-screen'))).toBeVisible();
            await element(by.id('import-wallet-button')).tap();

            // Import wallet screen
            await expect(element(by.id('import-wallet-screen'))).toBeVisible();

            // Enter test mnemonic
            const testMnemonic = 'abandon abandon abandon abandon abandon abandon abandon abandon abandon abandon abandon about';
            await element(by.id('mnemonic-input')).typeText(testMnemonic);
            await element(by.id('import-button')).tap();

            // PIN setup screen
            await expect(element(by.id('pin-setup-screen'))).toBeVisible();
            await element(by.id('pin-input')).typeText('123456');
            await element(by.id('confirm-pin-input')).typeText('123456');
            await element(by.id('set-pin-button')).tap();

            // Should navigate to main dashboard
            await waitFor(element(by.id('main-dashboard')))
                .toBeVisible()
                .withTimeout(5000);
        });
    });

    describe('Authentication Flow', () => {
        beforeEach(async () => {
            // Assume wallet is already set up
            await element(by.id('welcome-screen')).tap();
        });

        it('should authenticate with PIN', async () => {
            // PIN authentication screen
            await expect(element(by.id('pin-auth-screen'))).toBeVisible();
            await element(by.id('pin-input')).typeText('123456');
            await element(by.id('authenticate-button')).tap();

            // Should navigate to main dashboard
            await waitFor(element(by.id('main-dashboard')))
                .toBeVisible()
                .withTimeout(5000);
        });

        it('should handle incorrect PIN', async () => {
            // PIN authentication screen
            await expect(element(by.id('pin-auth-screen'))).toBeVisible();
            await element(by.id('pin-input')).typeText('000000');
            await element(by.id('authenticate-button')).tap();

            // Should show error message
            await expect(element(by.id('pin-error-message'))).toBeVisible();
        });
    });

    describe('Main Dashboard Navigation', () => {
        beforeEach(async () => {
            // Authenticate and navigate to dashboard
            await authenticateUser();
        });

        it('should navigate to wallet screen', async () => {
            await element(by.id('wallet-tab')).tap();
            await expect(element(by.id('wallet-screen'))).toBeVisible();
            await expect(element(by.id('wallet-balance'))).toBeVisible();
        });

        it('should navigate to trading screen', async () => {
            await element(by.id('trading-tab')).tap();
            await expect(element(by.id('trading-screen'))).toBeVisible();
            await expect(element(by.id('offers-list'))).toBeVisible();
        });

        it('should navigate to chat screen', async () => {
            await element(by.id('chat-tab')).tap();
            await expect(element(by.id('chat-screen'))).toBeVisible();
        });

        it('should navigate to profile screen', async () => {
            await element(by.id('profile-tab')).tap();
            await expect(element(by.id('profile-screen'))).toBeVisible();
        });
    });

    // Helper function to authenticate user
    async function authenticateUser() {
        try {
            await expect(element(by.id('pin-auth-screen'))).toBeVisible();
            await element(by.id('pin-input')).typeText('123456');
            await element(by.id('authenticate-button')).tap();
            await waitFor(element(by.id('main-dashboard')))
                .toBeVisible()
                .withTimeout(5000);
        } catch (error) {
            // User might already be authenticated
            console.log('User already authenticated or authentication not required');
        }
    }
});