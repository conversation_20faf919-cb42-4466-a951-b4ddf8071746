/**
 * Integration Test: Authentication Flow
 * Tests the complete wallet creation → authentication → API access flow
 */

import { MobileWalletUtils } from '../../utils/walletUtils';
import { walletAuthService } from '../../services/walletAuthService';
import AsyncStorage from '@react-native-async-storage/async-storage';

// Mock the API client
jest.mock('../../services/api', () => ({
  getApiClient: () => ({
    post: jest.fn(),
    get: jest.fn(),
  }),
}));

// Mock AsyncStorage
const mockAsyncStorage = {
  getItem: jest.fn(),
  setItem: jest.fn(),
  removeItem: jest.fn(),
};

jest.mock('@react-native-async-storage/async-storage', () => mockAsyncStorage);

describe('Authentication Flow Integration', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    mockAsyncStorage.getItem.mockResolvedValue(null);
    mockAsyncStorage.setItem.mockResolvedValue(undefined);
    mockAsyncStorage.removeItem.mockResolvedValue(undefined);
  });

  describe('Complete Wallet Creation and Authentication Flow', () => {
    it('should complete the full authentication flow', async () => {
      // Step 1: Generate a new wallet
      const mnemonic = MobileWalletUtils.generateMnemonic();
      expect(mnemonic).toBeDefined();
      expect(mnemonic.length).toBe(24);

      // Step 2: Generate wallet addresses
      const addresses = MobileWalletUtils.generateWalletAddresses(mnemonic);
      expect(addresses.ethereum).toMatch(/^0x[a-fA-F0-9]{40}$/);
      expect(addresses.bitcoin).toMatch(/^1[a-zA-Z0-9]+$/);
      expect(addresses.polygon).toMatch(/^0x[a-fA-F0-9]{40}$/);

      // Step 3: Create PIN hash
      const pin = '123456';
      const pinHash = MobileWalletUtils.hashPin(pin);
      expect(pinHash).toBeDefined();
      expect(typeof pinHash).toBe('string');

      // Step 4: Verify PIN
      const isPinValid = MobileWalletUtils.verifyPin(pin, pinHash);
      expect(isPinValid).toBe(true);

      // Step 5: Sign authentication message
      const authMessage = `Authenticate wallet ${addresses.ethereum} at ${Date.now()}`;
      const signature = await MobileWalletUtils.signMessage(mnemonic, authMessage);
      expect(signature).toBeDefined();
      expect(signature).toMatch(/^0x[a-fA-F0-9]+$/);

      // Step 6: Verify signature
      const isSignatureValid = MobileWalletUtils.verifySignature(
        authMessage,
        signature,
        addresses.ethereum
      );
      expect(isSignatureValid).toBe(true);

      // Step 7: Store wallet data
      const walletData = {
        mnemonic,
        addresses,
        pinHash,
        createdAt: Date.now(),
      };

      await mockAsyncStorage.setItem('wallet_address', addresses.ethereum);
      expect(mockAsyncStorage.setItem).toHaveBeenCalledWith(
        'wallet_address',
        addresses.ethereum
      );

      // Step 8: Store session token (simulated)
      const sessionToken = `session_${Date.now()}`;
      await mockAsyncStorage.setItem('session_token', sessionToken);
      expect(mockAsyncStorage.setItem).toHaveBeenCalledWith(
        'session_token',
        sessionToken
      );

      // Step 9: Retrieve stored data
      mockAsyncStorage.getItem.mockImplementation((key) => {
        if (key === 'wallet_address') return Promise.resolve(addresses.ethereum);
        if (key === 'session_token') return Promise.resolve(sessionToken);
        return Promise.resolve(null);
      });

      const storedAddress = await mockAsyncStorage.getItem('wallet_address');
      const storedToken = await mockAsyncStorage.getItem('session_token');

      expect(storedAddress).toBe(addresses.ethereum);
      expect(storedToken).toBe(sessionToken);
    });

    it('should handle authentication with stored wallet', async () => {
      // Simulate existing wallet data
      const mnemonic = MobileWalletUtils.generateMnemonic();
      const addresses = MobileWalletUtils.generateWalletAddresses(mnemonic);
      const pin = '123456';
      const pinHash = MobileWalletUtils.hashPin(pin);

      const walletData = {
        mnemonic,
        addresses,
        pinHash,
        createdAt: Date.now(),
      };

      // Mock stored wallet data
      mockAsyncStorage.getItem.mockImplementation((key) => {
        if (key === 'wallet_data') return Promise.resolve(JSON.stringify(walletData));
        if (key === 'wallet_address') return Promise.resolve(addresses.ethereum);
        return Promise.resolve(null);
      });

      // Step 1: Retrieve wallet data
      const storedWalletData = await mockAsyncStorage.getItem('wallet_data');
      expect(storedWalletData).toBeDefined();

      const parsedWalletData = JSON.parse(storedWalletData);
      expect(parsedWalletData.addresses.ethereum).toBe(addresses.ethereum);

      // Step 2: Verify PIN
      const isPinValid = MobileWalletUtils.verifyPin(pin, parsedWalletData.pinHash);
      expect(isPinValid).toBe(true);

      // Step 3: Sign authentication message
      const authMessage = `Re-authenticate wallet ${addresses.ethereum} at ${Date.now()}`;
      const signature = await MobileWalletUtils.signMessage(
        parsedWalletData.mnemonic,
        authMessage
      );
      expect(signature).toBeDefined();

      // Step 4: Verify signature matches wallet
      const isSignatureValid = MobileWalletUtils.verifySignature(
        authMessage,
        signature,
        addresses.ethereum
      );
      expect(isSignatureValid).toBe(true);
    });

    it('should handle PIN verification failure', () => {
      const correctPin = '123456';
      const wrongPin = '654321';
      const pinHash = MobileWalletUtils.hashPin(correctPin);

      const isCorrectPinValid = MobileWalletUtils.verifyPin(correctPin, pinHash);
      const isWrongPinValid = MobileWalletUtils.verifyPin(wrongPin, pinHash);

      expect(isCorrectPinValid).toBe(true);
      expect(isWrongPinValid).toBe(false);
    });

    it('should handle mnemonic encryption and decryption', () => {
      const mnemonic = MobileWalletUtils.generateMnemonic();
      const password = 'secure-password-123';

      // Encrypt mnemonic
      const encrypted = MobileWalletUtils.encryptMnemonic(mnemonic, password);
      expect(encrypted).toBeDefined();
      expect(typeof encrypted).toBe('string');

      // Decrypt mnemonic
      const decrypted = MobileWalletUtils.decryptMnemonic(encrypted, password);
      expect(decrypted).toEqual(mnemonic);

      // Test wrong password
      expect(() => {
        MobileWalletUtils.decryptMnemonic(encrypted, 'wrong-password');
      }).toThrow();
    });

    it('should generate consistent wallet data', () => {
      const testMnemonic = [
        'abandon', 'abandon', 'abandon', 'abandon', 'abandon', 'abandon',
        'abandon', 'abandon', 'abandon', 'abandon', 'abandon', 'abandon',
        'abandon', 'abandon', 'abandon', 'abandon', 'abandon', 'abandon',
        'abandon', 'abandon', 'abandon', 'abandon', 'abandon', 'art'
      ];

      // Generate addresses multiple times
      const addresses1 = MobileWalletUtils.generateWalletAddresses(testMnemonic);
      const addresses2 = MobileWalletUtils.generateWalletAddresses(testMnemonic);

      expect(addresses1).toEqual(addresses2);

      // Generate signatures multiple times
      const message = 'Test consistency message';
      return Promise.all([
        MobileWalletUtils.signMessage(testMnemonic, message),
        MobileWalletUtils.signMessage(testMnemonic, message)
      ]).then(([signature1, signature2]) => {
        expect(signature1).toEqual(signature2);
      });
    });
  });

  describe('Error Handling', () => {
    it('should handle invalid mnemonic gracefully', () => {
      const invalidMnemonic = ['invalid', 'mnemonic'];

      expect(() => {
        MobileWalletUtils.generateWalletAddresses(invalidMnemonic);
      }).not.toThrow(); // Should use fallback generation

      return MobileWalletUtils.signMessage(invalidMnemonic, 'test message')
        .then(signature => {
          expect(signature).toBeDefined();
          expect(typeof signature).toBe('string');
        });
    });

    it('should handle storage errors gracefully', async () => {
      mockAsyncStorage.setItem.mockRejectedValue(new Error('Storage error'));

      await expect(mockAsyncStorage.setItem('wallet_address', '0x123')).rejects.toThrow();
      await expect(mockAsyncStorage.setItem('session_token', 'token123')).rejects.toThrow();
    });

    it('should handle missing stored data', async () => {
      mockAsyncStorage.getItem.mockResolvedValue(null);

      const address = await mockAsyncStorage.getItem('wallet_address');
      const token = await mockAsyncStorage.getItem('session_token');

      expect(address).toBeNull();
      expect(token).toBeNull();
    });
  });
});
