/**
 * Environment Configuration for KryptoPesa Wallet
 * Handles different environments (development, staging, production)
 */

import Constants from 'expo-constants';

export interface EnvironmentConfig {
    API_BASE_URL: string;
    WS_BASE_URL: string;
    ENVIRONMENT: 'development' | 'staging' | 'production';
    DEBUG: boolean;
    API_TIMEOUT: number;
    WEBSOCKET_RECONNECT_ATTEMPTS: number;
    WEBSOCKET_RECONNECT_DELAY: number;
    SESSION_TIMEOUT: number;
    ENABLE_LOGGING: boolean;
}

// Default configuration
const DEFAULT_CONFIG: EnvironmentConfig = {
    API_BASE_URL: 'http://localhost:3000/api',
    WS_BASE_URL: 'ws://localhost:3000',
    ENVIRONMENT: 'development',
    DEBUG: true,
    API_TIMEOUT: 10000,
    WEBSOCKET_RECONNECT_ATTEMPTS: 5,
    WEBSOCKET_RECONNECT_DELAY: 1000,
    SESSION_TIMEOUT: 24 * 60 * 60 * 1000, // 24 hours
    ENABLE_LOGGING: true,
};

// Environment-specific configurations
const ENVIRONMENTS: Record<string, Partial<EnvironmentConfig>> = {
    development: {
        API_BASE_URL: 'http://localhost:3000/api',
        WS_BASE_URL: 'ws://localhost:3000',
        DEBUG: true,
        ENABLE_LOGGING: true,
    },
    staging: {
        API_BASE_URL: 'https://staging-api.kryptopesa.com/api',
        WS_BASE_URL: 'wss://staging-api.kryptopesa.com',
        DEBUG: true,
        ENABLE_LOGGING: true,
    },
    production: {
        API_BASE_URL: 'https://api.kryptopesa.com/api',
        WS_BASE_URL: 'wss://api.kryptopesa.com',
        DEBUG: false,
        ENABLE_LOGGING: false,
    },
};

/**
 * Get environment variable safely (works in both React Native and web)
 */
function getEnvVar(key: string): string | undefined {
    // Try to get from Constants.expoConfig.extra first (React Native)
    if (Constants.expoConfig?.extra?.[key]) {
        return Constants.expoConfig.extra[key];
    }

    // Try to get from process.env if available (Node.js/development)
    if (typeof process !== 'undefined' && process.env && process.env[key]) {
        return process.env[key];
    }

    return undefined;
}

/**
 * Get current environment name
 */
function getCurrentEnvironment(): string {
    // Check for explicit environment variable
    const explicitEnv = getEnvVar('EXPO_PUBLIC_ENVIRONMENT');
    if (explicitEnv) {
        return explicitEnv;
    }

    // Check NODE_ENV
    const nodeEnv = getEnvVar('NODE_ENV');
    if (nodeEnv === 'production') {
        return 'production';
    }

    // Default to development
    return 'development';
}

/**
 * Get environment configuration
 */
function getEnvironmentConfig(): EnvironmentConfig {
    const currentEnv = getCurrentEnvironment();
    const envConfig = ENVIRONMENTS[currentEnv] || {};

    // Override with environment variables if available
    const config: EnvironmentConfig = {
        ...DEFAULT_CONFIG,
        ...envConfig,
        ENVIRONMENT: currentEnv as any,
    };

    // Override with environment variables
    const apiBaseUrl = getEnvVar('EXPO_PUBLIC_API_BASE_URL');
    if (apiBaseUrl) {
        config.API_BASE_URL = apiBaseUrl;
    }

    const wsBaseUrl = getEnvVar('EXPO_PUBLIC_WS_BASE_URL');
    if (wsBaseUrl) {
        config.WS_BASE_URL = wsBaseUrl;
    }

    const debugEnv = getEnvVar('EXPO_PUBLIC_DEBUG');
    if (debugEnv) {
        config.DEBUG = debugEnv === 'true';
    }

    const apiTimeout = getEnvVar('EXPO_PUBLIC_API_TIMEOUT');
    if (apiTimeout) {
        config.API_TIMEOUT = parseInt(apiTimeout, 10);
    }

    return config;
}

// Export the configuration
export const ENV_CONFIG = getEnvironmentConfig();

// Helper functions
export const isProduction = () => ENV_CONFIG.ENVIRONMENT === 'production';
export const isDevelopment = () => ENV_CONFIG.ENVIRONMENT === 'development';
export const isStaging = () => ENV_CONFIG.ENVIRONMENT === 'staging';

// Logging helper
export const log = (...args: any[]) => {
    if (ENV_CONFIG.ENABLE_LOGGING) {
        console.log('[KryptoPesa]', ...args);
    }
};

export const logError = (...args: any[]) => {
    if (ENV_CONFIG.ENABLE_LOGGING) {
        console.error('[KryptoPesa Error]', ...args);
    }
};

export const logWarn = (...args: any[]) => {
    if (ENV_CONFIG.ENABLE_LOGGING) {
        console.warn('[KryptoPesa Warning]', ...args);
    }
};

// Network detection helper for mobile
export const getLocalNetworkIP = (): string => {
    // This would be used in development to auto-detect local IP
    // For now, return localhost as fallback
    return 'localhost';
};

// Validate configuration
export const validateConfig = (): boolean => {
    const requiredFields: (keyof EnvironmentConfig)[] = [
        'API_BASE_URL',
        'WS_BASE_URL',
        'ENVIRONMENT'
    ];

    for (const field of requiredFields) {
        if (!ENV_CONFIG[field]) {
            logError(`Missing required configuration: ${field}`);
            return false;
        }
    }

    return true;
};

// Initialize and validate configuration
if (!validateConfig()) {
    throw new Error('Invalid environment configuration');
}

log('Environment configuration loaded:', {
    environment: ENV_CONFIG.ENVIRONMENT,
    apiUrl: ENV_CONFIG.API_BASE_URL,
    wsUrl: ENV_CONFIG.WS_BASE_URL,
    debug: ENV_CONFIG.DEBUG,
});