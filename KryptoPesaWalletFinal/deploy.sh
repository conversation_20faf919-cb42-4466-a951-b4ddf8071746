#!/bin/bash

# KryptoPesa Production Deployment Script
# This script handles the complete deployment process for KryptoPesa

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging function
log() {
    echo -e "${BLUE}[$(date +'%Y-%m-%d %H:%M:%S')]${NC} $1"
}

error() {
    echo -e "${RED}[ERROR]${NC} $1" >&2
}

success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

# Check if required tools are installed
check_dependencies() {
    log "Checking dependencies..."

    local deps=("docker" "docker-compose" "git" "openssl")
    for dep in "${deps[@]}"; do
        if ! command -v "$dep" &> /dev/null; then
            error "$dep is not installed. Please install it first."
            exit 1
        fi
    done

    success "All dependencies are installed"
}

# Validate environment variables
validate_environment() {
    log "Validating environment variables..."

    local required_vars=(
        "DATABASE_URL"
        "REDIS_URL"
        "JWT_SECRET"
        "ENCRYPTION_KEY"
        "POSTGRES_DB"
        "POSTGRES_USER"
        "POSTGRES_PASSWORD"
        "REDIS_PASSWORD"
        "GRAFANA_PASSWORD"
    )

    local missing_vars=()
    for var in "${required_vars[@]}"; do
        if [[ -z "${!var}" ]]; then
            missing_vars+=("$var")
        fi
    done

    if [[ ${#missing_vars[@]} -gt 0 ]]; then
        error "Missing required environment variables:"
        printf '%s\n' "${missing_vars[@]}"
        exit 1
    fi

    success "Environment variables validated"
}

# Run security validation
run_security_checks() {
    log "Running security validation..."

    cd backend
    npm run security:validate
    cd ..

    success "Security validation passed"
}

# Run tests
run_tests() {
    log "Running test suite..."

    # Backend tests
    cd backend
    npm run test:coverage-check
    cd ..

    # Mobile app tests
    cd a0-project
    npm test -- --coverage --watchAll=false
    cd ..

    success "All tests passed"
}

# Generate SSL certificates (self-signed for development)
generate_ssl_certificates() {
    log "Generating SSL certificates..."

    if [[ ! -f "nginx/ssl/privkey.pem" ]] || [[ ! -f "nginx/ssl/fullchain.pem" ]]; then
        mkdir -p nginx/ssl

        # Generate self-signed certificate for development
        openssl req -x509 -nodes -days 365 -newkey rsa:2048 \
            -keyout nginx/ssl/privkey.pem \
            -out nginx/ssl/fullchain.pem \
            -subj "/C=KE/ST=Nairobi/L=Nairobi/O=KryptoPesa/CN=localhost"

        success "SSL certificates generated"
    else
        log "SSL certificates already exist"
    fi
}

# Create necessary directories
create_directories() {
    log "Creating necessary directories..."

    local dirs=(
        "backend/logs"
        "backend/uploads"
        "database/backups"
        "nginx/logs"
    )

    for dir in "${dirs[@]}"; do
        mkdir -p "$dir"
    done

    success "Directories created"
}

# Build and start services
deploy_services() {
    log "Building and deploying services..."

    # Pull latest images
    docker-compose -f docker-compose.prod.yml pull

    # Build custom images
    docker-compose -f docker-compose.prod.yml build --no-cache

    # Start services
    docker-compose -f docker-compose.prod.yml up -d

    success "Services deployed successfully"
}

# Wait for services to be healthy
wait_for_services() {
    log "Waiting for services to be healthy..."

    local max_attempts=30
    local attempt=1

    while [[ $attempt -le $max_attempts ]]; do
        if docker-compose -f docker-compose.prod.yml ps | grep -q "healthy"; then
            success "Services are healthy"
            return 0
        fi

        log "Attempt $attempt/$max_attempts - waiting for services..."
        sleep 10
        ((attempt++))
    done

    error "Services failed to become healthy within timeout"
    exit 1
}

# Main deployment function
main() {
    log "Starting KryptoPesa production deployment..."

    # Load environment variables if .env file exists
    if [[ -f ".env" ]]; then
        source .env
    fi

    check_dependencies
    validate_environment
    run_security_checks
    create_directories
    generate_ssl_certificates
    deploy_services
    wait_for_services

    success "🎉 KryptoPesa deployment completed successfully!"
    log "Access the application at: https://localhost"
    log "Monitoring dashboard: http://localhost:3001"
    log "Logs: docker-compose -f docker-compose.prod.yml logs -f"
}

# Handle script arguments
case "${1:-deploy}" in
    "deploy")
        main
        ;;
    "health")
        docker-compose -f docker-compose.prod.yml ps
        ;;
    "logs")
        docker-compose -f docker-compose.prod.yml logs -f "${2:-}"
        ;;
    "stop")
        docker-compose -f docker-compose.prod.yml down
        ;;
    "restart")
        docker-compose -f docker-compose.prod.yml restart "${2:-}"
        ;;
    *)
        echo "Usage: $0 {deploy|health|logs|stop|restart}"
        echo "  deploy  - Full deployment (default)"
        echo "  health  - Check service status"
        echo "  logs    - Show logs (optionally specify service)"
        echo "  stop    - Stop all services"
        echo "  restart - Restart services (optionally specify service)"
        exit 1
        ;;
esac