# KryptoPesa Production Environment Configuration
# Copy this file to .env and fill in your production values

# =============================================================================
# DATABASE CONFIGURATION
# =============================================================================
DATABASE_URL=***************************************************************/kryptopesa
POSTGRES_DB=kryptopesa
POSTGRES_USER=kryptopesa_user
POSTGRES_PASSWORD=your_secure_password_here

# =============================================================================
# REDIS CONFIGURATION
# =============================================================================
REDIS_URL=redis://:your_redis_password@redis:6379
REDIS_PASSWORD=your_secure_redis_password_here

# =============================================================================
# SECURITY CONFIGURATION
# =============================================================================
# Generate with: openssl rand -hex 32
JWT_SECRET=your_super_secure_jwt_secret_key_here_32_chars_minimum

# Generate with: openssl rand -hex 16
ENCRYPTION_KEY=your_32_character_encryption_key_here

# Generate with: openssl rand -hex 32
SESSION_SECRET=your_session_secret_key_here

# =============================================================================
# EXTERNAL SERVICES
# =============================================================================
# M-Pesa Integration (Safaricom)
MPESA_CONSUMER_KEY=your_mpesa_consumer_key
MPESA_CONSUMER_SECRET=your_mpesa_consumer_secret
MPESA_SHORTCODE=your_mpesa_shortcode
MPESA_PASSKEY=your_mpesa_passkey
MPESA_ENVIRONMENT=sandbox  # or 'production'

# Blockchain Configuration
BLOCKCHAIN_RPC_URL=https://your-blockchain-rpc-url
BLOCKCHAIN_PRIVATE_KEY=your_blockchain_private_key_for_escrow
BLOCKCHAIN_NETWORK=mainnet  # or 'testnet'

# =============================================================================
# AWS SERVICES (for file storage and services)
# =============================================================================
AWS_ACCESS_KEY_ID=your_aws_access_key_id
AWS_SECRET_ACCESS_KEY=your_aws_secret_access_key
AWS_REGION=us-east-1
AWS_S3_BUCKET=kryptopesa-uploads

# =============================================================================
# MONITORING & LOGGING
# =============================================================================
GRAFANA_PASSWORD=your_secure_grafana_password
PROMETHEUS_RETENTION=15d

# =============================================================================
# APPLICATION CONFIGURATION
# =============================================================================
NODE_ENV=production
PORT=3000
API_BASE_URL=https://api.kryptopesa.com
FRONTEND_URL=https://kryptopesa.com

# Rate limiting
RATE_LIMIT_WINDOW_MS=900000  # 15 minutes
RATE_LIMIT_MAX_REQUESTS=100

# Session configuration
SESSION_TIMEOUT=3600000  # 1 hour in milliseconds

# =============================================================================
# EMAIL CONFIGURATION (for notifications)
# =============================================================================
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASSWORD=your_email_password
FROM_EMAIL=<EMAIL>

# =============================================================================
# MOBILE APP CONFIGURATION
# =============================================================================
REACT_APP_API_URL=https://api.kryptopesa.com
REACT_APP_WS_URL=wss://api.kryptopesa.com
REACT_APP_ENVIRONMENT=production

# =============================================================================
# SECURITY HEADERS & CORS
# =============================================================================
CORS_ORIGIN=https://kryptopesa.com,https://www.kryptopesa.com
ALLOWED_HOSTS=kryptopesa.com,www.kryptopesa.com,api.kryptopesa.com

# =============================================================================
# BACKUP CONFIGURATION
# =============================================================================
BACKUP_SCHEDULE=0 2 * * *  # Daily at 2 AM
BACKUP_RETENTION_DAYS=30
BACKUP_S3_BUCKET=kryptopesa-backups

# =============================================================================
# LOGGING CONFIGURATION
# =============================================================================
LOG_LEVEL=info
LOG_FORMAT=json
LOG_MAX_SIZE=100m
LOG_MAX_FILES=10

# =============================================================================
# FEATURE FLAGS
# =============================================================================
ENABLE_REGISTRATION=true
ENABLE_TRADING=true
ENABLE_CHAT=true
ENABLE_NOTIFICATIONS=true
MAINTENANCE_MODE=false

# =============================================================================
# DEVELOPMENT/TESTING (set to false in production)
# =============================================================================
DEBUG_MODE=false
ENABLE_SWAGGER=false
ENABLE_CORS_ALL=false

# =============================================================================
# ADDITIONAL SECURITY
# =============================================================================
# Generate with: openssl rand -hex 32
WEBHOOK_SECRET=your_webhook_secret_for_external_services

# API Keys for external services
COINGECKO_API_KEY=your_coingecko_api_key_for_price_data
BLOCKCHAIN_EXPLORER_API_KEY=your_blockchain_explorer_api_key

# =============================================================================
# PRODUCTION DEPLOYMENT
# =============================================================================
DEPLOY_ENVIRONMENT=production
DEPLOY_VERSION=1.0.0
DEPLOY_TIMESTAMP=2025-01-15T00:00:00Z

# =============================================================================
# NOTES
# =============================================================================
# 1. Never commit this file with real values to version control
# 2. Use strong, unique passwords for all services
# 3. Rotate secrets regularly (every 90 days recommended)
# 4. Use environment-specific values for each deployment
# 5. Validate all environment variables on startup
# 6. Consider using a secret management service for production
PORT=3000
API_BASE_URL=http://localhost:3000

# Database Configuration
MONGODB_URI=mongodb://localhost:27017/kryptopesa
REDIS_URL=redis://localhost:6379

# JWT Configuration
JWT_SECRET=your-super-secret-jwt-key-change-this-in-production
JWT_EXPIRES_IN=7d
REFRESH_TOKEN_SECRET=your-refresh-token-secret

# Blockchain Configuration
# Polygon Network (Primary)
POLYGON_RPC_URL=https://polygon-rpc.com
POLYGON_CHAIN_ID=137
POLYGON_PRIVATE_KEY=your-polygon-private-key

# Ethereum Network (Fallback)
ETHEREUM_RPC_URL=https://mainnet.infura.io/v3/your-infura-key
ETHEREUM_CHAIN_ID=1
ETHEREUM_PRIVATE_KEY=your-ethereum-private-key

# Bitcoin Network
BITCOIN_NETWORK=mainnet
BITCOIN_RPC_URL=https://blockstream.info/api

# Smart Contract Addresses (Deploy and update these)
ESCROW_CONTRACT_ADDRESS=0x...
USDT_CONTRACT_ADDRESS=******************************************
USDC_CONTRACT_ADDRESS=******************************************

# External APIs
COINGECKO_API_KEY=your-coingecko-api-key
COINMARKETCAP_API_KEY=your-coinmarketcap-api-key

# Push Notifications
FIREBASE_SERVER_KEY=your-firebase-server-key
FIREBASE_PROJECT_ID=your-firebase-project-id

# File Upload
CLOUDINARY_CLOUD_NAME=your-cloudinary-name
CLOUDINARY_API_KEY=your-cloudinary-key
CLOUDINARY_API_SECRET=your-cloudinary-secret

# Email Configuration (for admin notifications)
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your-app-password

# Security
BCRYPT_ROUNDS=12
ENCRYPTION_KEY=your-32-character-encryption-key-change-this-in-production

# AWS Configuration (for enhanced key management)
AWS_REGION=us-east-1
AWS_ACCESS_KEY_ID=your-aws-access-key-id
AWS_SECRET_ACCESS_KEY=your-aws-secret-access-key
AWS_KMS_KEY_ID=your-aws-kms-key-id
AWS_SECRETS_MANAGER_REGION=us-east-1

# Key Management Configuration
KEY_ROTATION_INTERVAL_DAYS=30
KEY_ROTATION_WARNING_DAYS=7
KEY_ACCESS_LOG_RETENTION_DAYS=90
ENABLE_KEY_MONITORING=true
ENABLE_FALLBACK_STORAGE=true

# Rate Limiting Configuration (Production)
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# Comprehensive Monitoring Configuration
EMAIL_ALERTS_ENABLED=false
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your-email-password
ALERT_RECIPIENTS=<EMAIL>

# Discord Alerts
DISCORD_ALERTS_ENABLED=false
DISCORD_WEBHOOK_URL=your-discord-webhook-url

# Slack Alerts
SLACK_ALERTS_ENABLED=false
SLACK_WEBHOOK_URL=your-slack-webhook-url

# Metrics Authentication
METRICS_TOKEN=your-metrics-token

# Authentication Rate Limits
RATE_LIMIT_AUTH_MAX=5
RATE_LIMIT_LOGIN_MAX=3
RATE_LIMIT_PASSWORD_RESET_MAX=3

# Trading Rate Limits
RATE_LIMIT_TRADING_MAX=10
RATE_LIMIT_TRADE_CREATION_MAX=5

# Wallet Rate Limits
RATE_LIMIT_WALLET_MAX=20
RATE_LIMIT_WALLET_TX_MAX=5

# Chat Rate Limits
RATE_LIMIT_CHAT_MAX=30

# File Upload Rate Limits
RATE_LIMIT_FILE_UPLOAD_MAX=10

# Admin Rate Limits
RATE_LIMIT_ADMIN_MAX=50

# General API Rate Limits
RATE_LIMIT_GENERAL_MAX=1000
RATE_LIMIT_PUBLIC_MAX=100
RATE_LIMIT_HEALTH_MAX=200

# Advanced Rate Limiting
RATE_LIMIT_STRICT_MAX=3
RATE_LIMIT_PROGRESSIVE_BASE=100
RATE_LIMIT_PROGRESSIVE_WINDOW=900000

# Rate Limiting Alerts
RATE_LIMIT_ALERT_THRESHOLD=100
RATE_LIMIT_SUSPICIOUS_THRESHOLD=50
RATE_LIMIT_BLOCK_RATE_THRESHOLD=0.1

# Internal Service Token (for bypassing rate limits)
INTERNAL_SERVICE_TOKEN=your-internal-service-token-change-in-production

# Commission Settings
PLATFORM_COMMISSION_RATE=0.005
MINIMUM_TRADE_AMOUNT=10
MAXIMUM_TRADE_AMOUNT=50000

# Admin Configuration
ADMIN_EMAIL=<EMAIL>
ADMIN_PHONE=+254700000000

# Development Settings
DEBUG_MODE=true
LOG_LEVEL=debug
ENABLE_CORS=true
