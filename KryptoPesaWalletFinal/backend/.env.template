# KryptoPesa Production Environment Template
# CRITICAL: All values marked with ${} MUST be set via secure secret management
# DO NOT put actual secrets in this file - use AWS Secrets Manager, Hashi<PERSON><PERSON><PERSON> Vault, or K8s secrets

# Application Configuration
NODE_ENV=production
PORT=3000
API_VERSION=v1

# Secret Management Configuration
SECRET_PROVIDER=env  # Options: env, aws, vault, k8s
AWS_REGION=us-east-1  # Required if using AWS Secrets Manager

# Database Configuration (Production)
# CRITICAL: Use secure connection strings with authentication
MONGODB_URI=${MONGODB_URI}
MONGODB_OPTIONS=retryWrites=true&w=majority&readPreference=primary

# Redis Configuration (Production)
# CRITICAL: Must include authentication in production
REDIS_URL=${REDIS_URL}
REDIS_PASSWORD=${REDIS_PASSWORD}
REDIS_DB=0

# Security Configuration
# CRITICAL: These values MUST be set via secure secret management in production
# Use AWS Secrets Manager, <PERSON><PERSON><PERSON><PERSON><PERSON> Vault, or Kubernetes secrets
JWT_SECRET=${JWT_SECRET}
JWT_EXPIRES_IN=7d
JWT_REFRESH_EXPIRES_IN=30d

# Encryption Keys
# CRITICAL: Must be 32+ character random string, managed via secret store
ENCRYPTION_KEY=${ENCRYPTION_KEY}
SESSION_SECRET=${SESSION_SECRET}
HASH_ROUNDS=12

# CORS Configuration
CORS_ORIGINS=https://app.kryptopesa.com,https://admin.kryptopesa.com
CORS_CREDENTIALS=true

# Rate Limiting
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100
RATE_LIMIT_SKIP_SUCCESSFUL_REQUESTS=true

# Blockchain Configuration
# CRITICAL: RPC URLs and private keys must be secured via secret management
ETHEREUM_RPC_URL=${ETHEREUM_RPC_URL}
POLYGON_RPC_URL=${POLYGON_RPC_URL}
BLOCKCHAIN_PRIVATE_KEY=${BLOCKCHAIN_PRIVATE_KEY}

# Smart Contract Addresses (Public addresses, safe to include)
ESCROW_CONTRACT_ADDRESS=${ESCROW_CONTRACT_ADDRESS}
USDT_CONTRACT_ADDRESS=******************************************

# External Services
# CRITICAL: All API credentials must be managed via secret store
CLOUDINARY_CLOUD_NAME=${CLOUDINARY_CLOUD_NAME}
CLOUDINARY_API_KEY=${CLOUDINARY_API_KEY}
CLOUDINARY_API_SECRET=${CLOUDINARY_API_SECRET}

# Email Configuration
# CRITICAL: SMTP credentials must be secured
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_SECURE=false
SMTP_USER=${SMTP_USER}
SMTP_PASSWORD=${SMTP_PASSWORD}

# Push Notifications
# CRITICAL: Firebase credentials must be secured
FCM_SERVER_KEY=${FCM_SERVER_KEY}
FCM_PROJECT_ID=${FCM_PROJECT_ID}

# SSL Configuration (Production)
SSL_CERT_PATH=/etc/ssl/certs/kryptopesa.crt
SSL_KEY_PATH=/etc/ssl/private/kryptopesa.key
SSL_CA_PATH=/etc/ssl/certs/ca-bundle.crt

# Logging Configuration
LOG_LEVEL=info
LOG_FORMAT=json
LOG_FILE_PATH=/var/log/kryptopesa/app.log
LOG_MAX_SIZE=10m
LOG_MAX_FILES=5

# Monitoring Configuration
HEALTH_CHECK_ENDPOINT=/health
METRICS_ENDPOINT=/metrics
MONITORING_ENABLED=true

# Session Configuration
SESSION_STORE=redis
SESSION_TTL=86400
SESSION_SECURE=true
SESSION_HTTP_ONLY=true
SESSION_SAME_SITE=strict

# File Upload Configuration
UPLOAD_MAX_SIZE=10485760  # 10MB
UPLOAD_ALLOWED_TYPES=image/jpeg,image/png,image/gif,application/pdf
UPLOAD_DESTINATION=/var/uploads/kryptopesa

# Backup Configuration
BACKUP_ENABLED=true
BACKUP_SCHEDULE=0 2 * * *  # Daily at 2 AM
BACKUP_RETENTION_DAYS=30
BACKUP_S3_BUCKET=${BACKUP_S3_BUCKET}

# Performance Configuration
CLUSTER_WORKERS=auto
MEMORY_LIMIT=1024
CPU_LIMIT=2

# Security Headers
HSTS_MAX_AGE=31536000
CONTENT_SECURITY_POLICY=default-src 'self'; script-src 'self' 'unsafe-inline'; style-src 'self' 'unsafe-inline'

# API Documentation
API_DOCS_ENABLED=false  # Disable in production
SWAGGER_UI_ENABLED=false  # Disable in production