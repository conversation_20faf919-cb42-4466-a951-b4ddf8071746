// Simple server for testing - minimal dependencies
const path = require('path');
require('dotenv').config({ path: path.join(__dirname, '../.env') });

const express = require('express');
const cors = require('cors');
const helmet = require('helmet');
const compression = require('compression');
const morgan = require('morgan');
const { createServer } = require('http');
const { Server } = require('socket.io');

const { connectDB } = require('./config/database');
const logger = require('./utils/logger');
const { errorHandler } = require('./middleware/errorHandler');

// Import essential routes only
const walletAuthRoutes = require('./routes/walletAuth');
const userRoutes = require('./routes/user');
const walletRoutes = require('./routes/wallet');
const tradeRoutes = require('./routes/trade');
const offerRoutes = require('./routes/offer');
const tradingRoutes = require('./routes/trading');
const chatRoutes = require('./routes/chat');
const notificationRoutes = require('./routes/notifications');
const healthRoutes = require('./routes/health');

const app = express();
const server = createServer(app);

// Initialize Socket.IO
const io = new Server(server, {
  cors: {
    origin: process.env.FRONTEND_URL || "http://localhost:8081",
    methods: ["GET", "POST"],
    credentials: true
  }
});

// Basic middleware
app.use(helmet({
  crossOriginEmbedderPolicy: false,
  contentSecurityPolicy: false
}));

app.use(compression());
app.use(morgan('combined'));

// CORS configuration
app.use(cors({
  origin: function (origin, callback) {
    const allowedOrigins = [
      'http://localhost:8081',
      'http://localhost:19006',
      'http://***********:8081',
      'http://***********:19006'
    ];
    
    if (!origin || allowedOrigins.includes(origin)) {
      callback(null, true);
    } else {
      callback(new Error('Not allowed by CORS'));
    }
  },
  credentials: true
}));

// Body parsing middleware
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));

// Connect to MongoDB only
(async () => {
  try {
    await connectDB();
    logger.info('MongoDB connected successfully');
  } catch (error) {
    logger.error('MongoDB connection failed:', error);
    process.exit(1);
  }
})();

// Debug middleware
app.use((req, res, next) => {
  if (req.url.startsWith('/api/')) {
    console.log('🔍 [SIMPLE SERVER] Request:', {
      method: req.method,
      url: req.url,
      headers: {
        'x-wallet-address': req.headers['x-wallet-address'],
        'content-type': req.headers['content-type'],
        'origin': req.headers['origin']
      }
    });
  }
  next();
});

// Health check endpoint
app.get('/api/health', (req, res) => {
  res.json({
    status: 'ok',
    timestamp: new Date().toISOString(),
    service: 'KryptoPesa-Backend-Simple',
    version: '1.0.0'
  });
});

// Debug endpoint to test offers directly
app.get('/api/debug/offers', async (req, res) => {
  try {
    const Offer = require('./models/Offer');
    const offers = await Offer.find({
      status: 'active',
      expiresAt: { $gt: new Date() }
    }).populate('creator', 'username profile');

    res.json({
      success: true,
      count: offers.length,
      offers: offers
    });
  } catch (error) {
    res.json({
      success: false,
      error: error.message,
      stack: error.stack
    });
  }
});

// API Routes
app.use('/api/auth', walletAuthRoutes);
app.use('/api/users', userRoutes);
app.use('/api/wallet', walletRoutes);
app.use('/api/trades', tradeRoutes);
app.use('/api/offers', offerRoutes);
app.use('/api/trading', tradingRoutes);
app.use('/api/chat', chatRoutes);
app.use('/api/notifications', notificationRoutes);
app.use('/api/health', healthRoutes);

// Socket.IO connection handling
io.on('connection', (socket) => {
  console.log('🔌 Client connected:', socket.id);

  socket.on('join-room', (room) => {
    socket.join(room);
    console.log(`📡 Socket ${socket.id} joined room: ${room}`);
  });

  socket.on('leave-room', (room) => {
    socket.leave(room);
    console.log(`📡 Socket ${socket.id} left room: ${room}`);
  });

  socket.on('chat-message', (data) => {
    console.log('💬 Chat message:', data);
    socket.to(data.room).emit('chat-message', data);
  });

  socket.on('trade-update', (data) => {
    console.log('📊 Trade update:', data);
    socket.to(data.room).emit('trade-update', data);
  });

  socket.on('disconnect', () => {
    console.log('🔌 Client disconnected:', socket.id);
  });
});

// Error handling middleware
app.use(errorHandler);

// 404 handler
app.use('*', (req, res) => {
  res.status(404).json({
    success: false,
    message: 'Route not found',
    path: req.originalUrl
  });
});

// Start server
const PORT = process.env.PORT || 3000;
server.listen(PORT, () => {
  console.log(`🚀 KryptoPesa Simple Backend Server running on port ${PORT}`);
  console.log(`📱 Frontend URL: ${process.env.FRONTEND_URL || 'http://localhost:8081'}`);
  console.log(`🌐 Environment: ${process.env.NODE_ENV || 'development'}`);
  logger.info('Simple server started successfully', {
    port: PORT,
    environment: process.env.NODE_ENV || 'development'
  });
});

// Graceful shutdown
process.on('SIGTERM', () => {
  console.log('🛑 SIGTERM received, shutting down gracefully');
  server.close(() => {
    console.log('✅ Server closed');
    process.exit(0);
  });
});

process.on('SIGINT', () => {
  console.log('🛑 SIGINT received, shutting down gracefully');
  server.close(() => {
    console.log('✅ Server closed');
    process.exit(0);
  });
});

module.exports = { app, server, io };
