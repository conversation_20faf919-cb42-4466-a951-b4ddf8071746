const path = require('path');
require('dotenv').config({ path: path.join(__dirname, '../../.env') });

const mongoose = require('mongoose');
const bcrypt = require('bcryptjs');
const { connectDB } = require('../config/database');

// Import models
const User = require('../models/User');
const Offer = require('../models/Offer');

async function seedRealData() {
    try {
        console.log('🌱 Starting to seed real data...');

        // Connect to database
        await connectDB();
        console.log('✅ Connected to MongoDB');

        // Clear existing data
        await User.deleteMany({});
        await Offer.deleteMany({});
        console.log('🧹 Cleared existing data');

        // Hash password for users
        const hashedPassword = await bcrypt.hash('SecurePassword123!', 12);

        // Create test users with correct schema
        const users = await User.insertMany([
            {
                username: 'alice_trader',
                email: '<EMAIL>',
                phone: '+************',
                password: hashedPassword,
                walletAddress: '******************************************',
                profile: {
                    firstName: 'Alice',
                    lastName: 'Johnson',
                    bio: 'Experienced crypto trader with 3+ years in P2P trading',
                    location: {
                        country: 'KE',
                        city: 'Nairobi'
                    },
                    avatar: 'https://images.unsplash.com/photo-1494790108755-2616b612b786?w=150',
                    preferredLanguage: 'en'
                },
                verification: {
                    email: { verified: true },
                    phone: { verified: true },
                    identity: { verified: true }
                },
                status: 'active',
                lastActive: new Date()
            },
            {
                username: 'bob_crypto',
                email: '<EMAIL>',
                phone: '+254723456789',
                password: hashedPassword,
                walletAddress: '0x8ba1f109551bD432803012645Hac136c22C177e9',
                profile: {
                    firstName: 'Bob',
                    lastName: 'Mwangi',
                    bio: 'Bitcoin enthusiast and DeFi explorer. Quick and reliable trades.',
                    location: {
                        country: 'KE',
                        city: 'Mombasa'
                    },
                    avatar: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150',
                    preferredLanguage: 'en'
                },
                verification: {
                    email: { verified: true },
                    phone: { verified: true },
                    identity: { verified: true }
                },
                status: 'active',
                lastActive: new Date()
            },
            {
                username: 'carol_defi',
                email: '<EMAIL>',
                phone: '+254734567890',
                password: hashedPassword,
                walletAddress: '0x****************************************',
                profile: {
                    firstName: 'Carol',
                    lastName: 'Wanjiku',
                    bio: 'Professional trader specializing in stablecoins and DeFi protocols.',
                    location: {
                        country: 'KE',
                        city: 'Kisumu'
                    },
                    avatar: 'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=150',
                    preferredLanguage: 'en'
                },
                verification: {
                    email: { verified: true },
                    phone: { verified: true },
                    identity: { verified: true }
                },
                status: 'active',
                lastActive: new Date()
            }
        ]);

        console.log(`✅ Created ${users.length} test users`);

        // Create realistic offers
        const offers = await Offer.insertMany([
            {
                offerId: 'OFFER_' + Date.now() + '_1',
                creator: users[0]._id,
                type: 'sell',
                cryptocurrency: {
                    symbol: 'USDT',
                    contractAddress: '******************************************',
                    network: 'polygon',
                    minAmount: '500',
                    maxAmount: '1000',
                    availableAmount: '1000'
                },
                fiat: {
                    currency: 'KES',
                    priceType: 'fixed',
                    fixedPrice: 152.50,
                    effectivePrice: 152.50
                },
                paymentMethods: [
                    {
                        method: 'mobile_money',
                        details: {
                            provider: 'M-Pesa',
                            mobileNumber: '+************',
                            instructions: 'Send payment to this number and provide transaction ID'
                        },
                        isPreferred: true
                    },
                    {
                        method: 'bank_transfer',
                        details: {
                            bankName: 'KCB Bank',
                            accountNumber: '**********',
                            accountName: 'Alice Johnson',
                            instructions: 'Use reference: USDT-TRADE'
                        },
                        isPreferred: false
                    }
                ],
                terms: {
                    timeLimit: 30,
                    instructions: 'Quick release after payment confirmation. Available 9 AM - 9 PM EAT. Minimum trade 500 USDT.',
                    autoRelease: true
                },
                location: {
                    country: 'KE',
                    city: 'Nairobi'
                },
                status: 'active',
                visibility: 'public',
                expiresAt: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000) // 30 days from now
            },
            {
                offerId: 'OFFER_' + Date.now() + '_2',
                creator: users[1]._id,
                type: 'buy',
                cryptocurrency: {
                    symbol: 'BTC',
                    network: 'bitcoin',
                    minAmount: '0.1',
                    maxAmount: '0.5',
                    availableAmount: '0.5'
                },
                fiat: {
                    currency: 'KES',
                    priceType: 'fixed',
                    fixedPrice: 7250000,
                    effectivePrice: 7250000
                },
                paymentMethods: [
                    {
                        method: 'mobile_money',
                        details: {
                            provider: 'M-Pesa',
                            mobileNumber: '+254723456789',
                            instructions: 'Fast payment guaranteed. Send to this number.'
                        },
                        isPreferred: true
                    }
                ],
                terms: {
                    timeLimit: 15,
                    instructions: 'Looking for quick BTC purchase. Payment within 15 minutes. Trusted sellers only.',
                    autoRelease: false
                },
                location: {
                    country: 'KE',
                    city: 'Mombasa'
                },
                status: 'active',
                visibility: 'public',
                expiresAt: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000) // 30 days from now
            }
        ]);

        console.log(`✅ Created ${offers.length} test offers`);

        console.log('🎉 Database seeded successfully with real data!');
        console.log('\n📊 Summary:');
        console.log(`- Users: ${users.length}`);
        console.log(`- Offers: ${offers.length}`);

    } catch (error) {
        console.error('❌ Error seeding database:', error);
    } finally {
        await mongoose.connection.close();
        console.log('🔌 Database connection closed');
        process.exit(0);
    }
}

// Run the seeding
seedRealData();