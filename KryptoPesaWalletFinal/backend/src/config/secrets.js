/**
 * Secure Secret Management System
 * Supports AWS Secrets Manager, HashiCorp Vault, and Kubernetes Secrets
 */

const crypto = require('crypto');
const logger = require('../utils/logger');

class SecretManager {
    constructor() {
        this.provider = process.env.SECRET_PROVIDER || 'env'; // env, aws, vault, k8s
        this.cache = new Map();
        this.cacheTimeout = 300000; // 5 minutes
        this.initializeProvider();
    }

    initializeProvider() {
        switch (this.provider) {
            case 'aws':
                try {
                    const AWS = require('aws-sdk');
                    this.awsSecretsManager = new AWS.SecretsManager({
                        region: process.env.AWS_REGION || 'us-east-1'
                    });
                } catch (error) {
                    logger.warn('AWS SDK not available, falling back to environment variables');
                    this.provider = 'env';
                }
                break;
            case 'vault':
                logger.info('Vault provider configured');
                break;
            case 'k8s':
                logger.info('Kubernetes secrets provider configured');
                break;
            default:
                logger.info('Using environment variables for secrets (development only)');
        }
    }

    async getSecret(secretName, defaultValue = null) {
        try {
            // Check cache first
            const cached = this.cache.get(secretName);
            if (cached && Date.now() - cached.timestamp < this.cacheTimeout) {
                return cached.value;
            }

            let secretValue;

            switch (this.provider) {
                case 'aws':
                    secretValue = await this.getAWSSecret(secretName);
                    break;
                case 'vault':
                    secretValue = await this.getVaultSecret(secretName);
                    break;
                case 'k8s':
                    secretValue = await this.getK8sSecret(secretName);
                    break;
                default:
                    secretValue = process.env[secretName] || defaultValue;
            }

            // Cache the secret
            if (secretValue) {
                this.cache.set(secretName, {
                    value: secretValue,
                    timestamp: Date.now()
                });
            }

            return secretValue;
        } catch (error) {
            logger.error(`Failed to retrieve secret ${secretName}:`, error);
            return defaultValue;
        }
    }

    async getAWSSecret(secretName) {
        try {
            const result = await this.awsSecretsManager.getSecretValue({
                SecretId: secretName
            }).promise();

            if (result.SecretString) {
                const secret = JSON.parse(result.SecretString);
                return secret[secretName] || secret.value;
            }

            return result.SecretBinary;
        } catch (error) {
            logger.error(`AWS Secrets Manager error for ${secretName}:`, error);
            return process.env[secretName];
        }
    }

    async getVaultSecret(secretName) {
        logger.warn('Vault integration not implemented, using environment variable');
        return process.env[secretName];
    }

    async getK8sSecret(secretName) {
        const fs = require('fs').promises;
        try {
            const secretPath = `/var/secrets/${secretName}`;
            return await fs.readFile(secretPath, 'utf8');
        } catch (error) {
            logger.warn(`K8s secret not found for ${secretName}, using environment variable`);
            return process.env[secretName];
        }
    }

    generateSecureSecret(length = 32) {
        return crypto.randomBytes(length).toString('hex');
    }

    clearCache() {
        this.cache.clear();
    }

    // Validate that all required secrets are available and secure
    async validateSecrets() {
        const requiredSecrets = [
            'JWT_SECRET',
            'ENCRYPTION_KEY',
            'MONGODB_URI',
            'REDIS_URL'
        ];

        if (process.env.NODE_ENV === 'production') {
            requiredSecrets.push(
                'REDIS_PASSWORD',
                'BLOCKCHAIN_PRIVATE_KEY',
                'SMTP_PASSWORD',
                'FCM_SERVER_KEY',
                'CLOUDINARY_API_SECRET'
            );
        }

        const missingSecrets = [];
        const insecureSecrets = [];

        for (const secretName of requiredSecrets) {
            const value = await this.getSecret(secretName);

            if (!value) {
                missingSecrets.push(secretName);
            } else if (this.isInsecureValue(value)) {
                insecureSecrets.push(secretName);
            }
        }

        if (missingSecrets.length > 0) {
            throw new Error(`Missing secrets: ${missingSecrets.join(', ')}`);
        }

        if (insecureSecrets.length > 0) {
            throw new Error(`Insecure/placeholder secrets detected: ${insecureSecrets.join(', ')}`);
        }

        logger.info('All required secrets validated successfully');
        return true;
    }

    isInsecureValue(value) {
        const insecurePatterns = [
            'CHANGE_ME',
            'your-',
            'placeholder',
            'example',
            'test',
            'demo',
            '123456',
            'password'
        ];

        return insecurePatterns.some(pattern =>
            value.toLowerCase().includes(pattern.toLowerCase())
        ) || value.length < 8;
    }
}

// Singleton instance
const secretManager = new SecretManager();

module.exports = {
    SecretManager,
    secretManager,
    getSecret: (name, defaultValue) => secretManager.getSecret(name, defaultValue),
    validateSecrets: () => secretManager.validateSecrets()
};