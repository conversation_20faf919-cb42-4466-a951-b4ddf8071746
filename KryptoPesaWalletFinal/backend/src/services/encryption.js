/**
 * Encryption Service for Data at Rest
 * Provides AES-256-GCM encryption for sensitive data
 */

const crypto = require('crypto');
const { getSecret } = require('../config/secrets');
const logger = require('../utils/logger');

class EncryptionService {
    constructor() {
        this.algorithm = 'aes-256-gcm';
        this.keyLength = 32; // 256 bits
        this.ivLength = 16; // 128 bits
        this.tagLength = 16; // 128 bits
        this.encryptionKey = null;
    }

    async initialize() {
        try {
            const key = await getSecret('ENCRYPTION_KEY');
            if (!key) {
                throw new Error('ENCRYPTION_KEY not found in secret manager');
            }

            // Derive a consistent key from the secret
            this.encryptionKey = crypto.scryptSync(key, 'kryptopesa-salt', this.keyLength);
            logger.info('Encryption service initialized successfully');
        } catch (error) {
            logger.error('Failed to initialize encryption service:', error);
            throw error;
        }
    }

    /**
     * Encrypt sensitive data
     * @param {string} plaintext - Data to encrypt
     * @returns {string} - Base64 encoded encrypted data with IV and tag
     */
    encrypt(plaintext) {
        try {
            if (!this.encryptionKey) {
                throw new Error('Encryption service not initialized');
            }

            const iv = crypto.randomBytes(this.ivLength);
            const cipher = crypto.createCipher(this.algorithm, this.encryptionKey, { iv });

            let encrypted = cipher.update(plaintext, 'utf8', 'hex');
            encrypted += cipher.final('hex');

            const tag = cipher.getAuthTag();

            // Combine IV, tag, and encrypted data
            const combined = Buffer.concat([iv, tag, Buffer.from(encrypted, 'hex')]);
            return combined.toString('base64');
        } catch (error) {
            logger.error('Encryption failed:', error);
            throw new Error('Failed to encrypt data');
        }
    }

    /**
     * Decrypt sensitive data
     * @param {string} encryptedData - Base64 encoded encrypted data
     * @returns {string} - Decrypted plaintext
     */
    decrypt(encryptedData) {
        try {
            if (!this.encryptionKey) {
                throw new Error('Encryption service not initialized');
            }

            const combined = Buffer.from(encryptedData, 'base64');

            // Extract IV, tag, and encrypted data
            const iv = combined.slice(0, this.ivLength);
            const tag = combined.slice(this.ivLength, this.ivLength + this.tagLength);
            const encrypted = combined.slice(this.ivLength + this.tagLength);

            const decipher = crypto.createDecipher(this.algorithm, this.encryptionKey, { iv });
            decipher.setAuthTag(tag);

            let decrypted = decipher.update(encrypted, null, 'utf8');
            decrypted += decipher.final('utf8');

            return decrypted;
        } catch (error) {
            logger.error('Decryption failed:', error);
            throw new Error('Failed to decrypt data');
        }
    }

    /**
     * Hash sensitive data (one-way)
     * @param {string} data - Data to hash
     * @param {string} salt - Optional salt
     * @returns {string} - Hex encoded hash
     */
    hash(data, salt = 'kryptopesa-default-salt') {
        try {
            return crypto.scryptSync(data, salt, 64).toString('hex');
        } catch (error) {
            logger.error('Hashing failed:', error);
            throw new Error('Failed to hash data');
        }
    }

    /**
     * Generate secure random token
     * @param {number} length - Token length in bytes
     * @returns {string} - Hex encoded token
     */
    generateToken(length = 32) {
        return crypto.randomBytes(length).toString('hex');
    }

    /**
     * Encrypt field for database storage
     * @param {Object} document - Mongoose document
     * @param {string} fieldName - Field to encrypt
     */
    encryptField(document, fieldName) {
        if (document[fieldName] && typeof document[fieldName] === 'string') {
            document[fieldName] = this.encrypt(document[fieldName]);
        }
    }

    /**
     * Decrypt field from database
     * @param {Object} document - Mongoose document
     * @param {string} fieldName - Field to decrypt
     */
    decryptField(document, fieldName) {
        if (document[fieldName] && typeof document[fieldName] === 'string') {
            try {
                document[fieldName] = this.decrypt(document[fieldName]);
            } catch (error) {
                // Field might not be encrypted (backward compatibility)
                logger.warn(`Failed to decrypt field ${fieldName}, assuming plaintext`);
            }
        }
    }

    /**
     * Encrypt multiple fields in a document
     * @param {Object} document - Document to encrypt
     * @param {Array} fields - Array of field names to encrypt
     */
    encryptFields(document, fields) {
        fields.forEach(field => this.encryptField(document, field));
    }

    /**
     * Decrypt multiple fields in a document
     * @param {Object} document - Document to decrypt
     * @param {Array} fields - Array of field names to decrypt
     */
    decryptFields(document, fields) {
        fields.forEach(field => this.decryptField(document, field));
    }
}

// Singleton instance
const encryptionService = new EncryptionService();

module.exports = {
    EncryptionService,
    encryptionService
};