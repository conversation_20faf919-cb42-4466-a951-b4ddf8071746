/**
 * Advanced Connection Pool Optimizer for 50K+ Daily Users
 * Manages database connections efficiently for high-traffic scenarios
 */

const mongoose = require('mongoose');
const logger = require('../utils/logger');
const { getRedisClient } = require('../config/redis');

class ConnectionPoolOptimizer {
  constructor() {
    this.poolMetrics = {
      activeConnections: 0,
      idleConnections: 0,
      totalConnections: 0,
      connectionErrors: 0,
      averageConnectionTime: 0,
      peakConnections: 0,
      connectionHistory: []
    };

    this.redisClient = getRedisClient();
    this.optimizationRules = new Map();
    this.initializeOptimization();
  }

  /**
   * Initialize connection pool optimization
   */
  initializeOptimization() {
    // Set up connection pool for high traffic
    this.configureMongoosePool();

    // Set up Redis connection pool
    this.configureRedisPool();

    // Start monitoring
    this.startConnectionMonitoring();

    // Set up auto-scaling rules
    this.setupAutoScalingRules();
  }

  /**
   * Configure Mongoose connection pool for 50K+ users
   */
  configureMongoosePool() {
    const poolConfig = {
      maxPoolSize: parseInt(process.env.MONGO_MAX_POOL_SIZE) || 100,
      minPoolSize: parseInt(process.env.MONGO_MIN_POOL_SIZE) || 10,
      maxIdleTimeMS: parseInt(process.env.MONGO_MAX_IDLE_TIME) || 30000,
      serverSelectionTimeoutMS: 5000,
      socketTimeoutMS: 45000,
      heartbeatFrequencyMS: 10000,
      maxConnecting: 10,
      bufferMaxEntries: 0, // Disable mongoose buffering
      bufferCommands: false,

      // Connection pool events
      useNewUrlParser: true,
      useUnifiedTopology: true
    };

    // Apply configuration
    mongoose.set('bufferCommands', false);

    // Monitor connection events
    mongoose.connection.on('connected', () => {
      logger.info('MongoDB connected with optimized pool');
      this.poolMetrics.totalConnections++;
    });

    mongoose.connection.on('error', (err) => {
      logger.error('MongoDB connection error:', err);
      this.poolMetrics.connectionErrors++;
    });

    mongoose.connection.on('disconnected', () => {
      logger.warn('MongoDB disconnected');
      this.poolMetrics.totalConnections = Math.max(0, this.poolMetrics.totalConnections - 1);
    });

    logger.info('Mongoose connection pool configured for high traffic');
  }

  /**
   * Configure Redis connection pool
   */
  configureRedisPool() {
    try {
      // Redis is already configured in the redis config
      // Monitor Redis connection health
      this.redisClient.on('connect', () => {
        logger.info('Redis connected');
      });

      this.redisClient.on('error', (err) => {
        logger.error('Redis connection error:', err);
      });

      logger.info('Redis connection monitoring configured');
    } catch (error) {
      logger.error('Error configuring Redis pool:', error);
    }
  }

  /**
   * Start connection monitoring
   */
  startConnectionMonitoring() {
    setInterval(() => {
      this.collectConnectionMetrics();
      this.optimizeConnectionPool();
    }, 30000); // Every 30 seconds

    setInterval(() => {
      this.generateConnectionReport();
    }, 300000); // Every 5 minutes
  }

  /**
   * Collect real-time connection metrics
   */
  async collectConnectionMetrics() {
    try {
      // Get MongoDB connection stats
      const mongoStats = await this.getMongoConnectionStats();

      // Get Redis connection stats
      const redisStats = await this.getRedisConnectionStats();

      // Update metrics
      this.poolMetrics.activeConnections = mongoStats.active;
      this.poolMetrics.idleConnections = mongoStats.idle;
      this.poolMetrics.totalConnections = mongoStats.total;

      // Track peak connections
      if (mongoStats.total > this.poolMetrics.peakConnections) {
        this.poolMetrics.peakConnections = mongoStats.total;
      }

      // Store historical data
      this.poolMetrics.connectionHistory.push({
        timestamp: new Date(),
        active: mongoStats.active,
        idle: mongoStats.idle,
        total: mongoStats.total,
        redis: redisStats
      });

      // Keep only last 100 entries
      if (this.poolMetrics.connectionHistory.length > 100) {
        this.poolMetrics.connectionHistory.shift();
      }

    } catch (error) {
      logger.error('Error collecting connection metrics:', error);
    }
  }

  /**
   * Get MongoDB connection statistics
   */
  async getMongoConnectionStats() {
    try {
      const adminDb = mongoose.connection.db.admin();
      const serverStatus = await adminDb.serverStatus();

      return {
        active: serverStatus.connections?.active || 0,
        idle: serverStatus.connections?.available || 0,
        total: serverStatus.connections?.current || 0,
        created: serverStatus.connections?.totalCreated || 0
      };
    } catch (error) {
      logger.error('Error getting MongoDB stats:', error);
      return { active: 0, idle: 0, total: 0, created: 0 };
    }
  }

  /**
   * Get Redis connection statistics
   */
  async getRedisConnectionStats() {
    try {
      const info = await this.redisClient.info('clients');
      const lines = info.split('\r\n');
      const stats = {};

      lines.forEach(line => {
        if (line.includes(':')) {
          const [key, value] = line.split(':');
          stats[key] = parseInt(value) || value;
        }
      });

      return {
        connected: stats.connected_clients || 0,
        blocked: stats.blocked_clients || 0,
        tracking: stats.tracking_clients || 0
      };
    } catch (error) {
      logger.error('Error getting Redis stats:', error);
      return { connected: 0, blocked: 0, tracking: 0 };
    }
  }

  /**
   * Optimize connection pool based on current load
   */
  optimizeConnectionPool() {
    const utilizationRate = this.poolMetrics.activeConnections /
      (this.poolMetrics.activeConnections + this.poolMetrics.idleConnections);

    // High utilization - consider scaling up
    if (utilizationRate > 0.8) {
      this.scaleUpConnections();
    }

    // Low utilization - consider scaling down
    else if (utilizationRate < 0.3 && this.poolMetrics.totalConnections > 10) {
      this.scaleDownConnections();
    }

    // Log optimization actions
    logger.debug(`Connection pool utilization: ${(utilizationRate * 100).toFixed(2)}%`);
  }

  /**
   * Scale up connections for high load
   */
  scaleUpConnections() {
    logger.info('High connection utilization detected - scaling up');

    // This would typically involve:
    // 1. Increasing maxPoolSize temporarily
    // 2. Alerting infrastructure for horizontal scaling
    // 3. Implementing connection queuing

    this.alertInfrastructure('scale_up', {
      reason: 'high_connection_utilization',
      currentUtilization: this.poolMetrics.activeConnections / this.poolMetrics.totalConnections,
      recommendation: 'increase_pool_size'
    });
  }

  /**
   * Scale down connections for low load
   */
  scaleDownConnections() {
    logger.info('Low connection utilization detected - scaling down');

    this.alertInfrastructure('scale_down', {
      reason: 'low_connection_utilization',
      currentUtilization: this.poolMetrics.activeConnections / this.poolMetrics.totalConnections,
      recommendation: 'decrease_pool_size'
    });
  }

  /**
   * Setup auto-scaling rules
   */
  setupAutoScalingRules() {
    this.optimizationRules.set('high_load', {
      condition: () => this.poolMetrics.activeConnections > 80,
      action: () => this.scaleUpConnections(),
      cooldown: 60000 // 1 minute
    });

    this.optimizationRules.set('connection_errors', {
      condition: () => this.poolMetrics.connectionErrors > 10,
      action: () => this.handleConnectionErrors(),
      cooldown: 30000 // 30 seconds
    });

    this.optimizationRules.set('memory_pressure', {
      condition: () => this.getMemoryUsage() > 0.85,
      action: () => this.optimizeForMemory(),
      cooldown: 120000 // 2 minutes
    });
  }

  /**
   * Handle connection errors
   */
  handleConnectionErrors() {
    logger.warn('High connection error rate detected');

    // Reset error counter
    this.poolMetrics.connectionErrors = 0;

    // Alert monitoring systems
    this.alertInfrastructure('connection_errors', {
      errorCount: this.poolMetrics.connectionErrors,
      recommendation: 'check_database_health'
    });
  }

  /**
   * Optimize for memory usage
   */
  optimizeForMemory() {
    logger.info('Optimizing connection pool for memory usage');

    // Reduce idle connections
    // Force garbage collection if available
    if (global.gc) {
      global.gc();
    }
  }

  /**
   * Get current memory usage
   */
  getMemoryUsage() {
    const memUsage = process.memoryUsage();
    return memUsage.heapUsed / memUsage.heapTotal;
  }

  /**
   * Alert infrastructure systems
   */
  alertInfrastructure(type, data) {
    // This would integrate with monitoring systems like Prometheus, Grafana, etc.
    logger.info(`Infrastructure alert: ${type}`, data);

    // Store alert in Redis for monitoring dashboard (skip if Redis unavailable)
    if (this.redisClient) {
      this.redisClient.lpush('infrastructure_alerts', JSON.stringify({
        type,
        data,
        timestamp: new Date().toISOString()
      }));
    } else {
      console.warn('[ConnectionPoolOptimizer] Infrastructure alert (Redis unavailable):', type, data);
    }
  }

  /**
   * Generate connection performance report
   */
  generateConnectionReport() {
    const report = {
      timestamp: new Date().toISOString(),
      metrics: this.poolMetrics,
      recommendations: this.generateRecommendations(),
      health: this.assessConnectionHealth()
    };

    logger.info('Connection pool report generated', report);
    return report;
  }

  /**
   * Generate optimization recommendations
   */
  generateRecommendations() {
    const recommendations = [];

    const utilizationRate = this.poolMetrics.activeConnections / this.poolMetrics.totalConnections;

    if (utilizationRate > 0.9) {
      recommendations.push('Consider increasing maximum pool size');
    }

    if (this.poolMetrics.connectionErrors > 5) {
      recommendations.push('Investigate connection stability issues');
    }

    if (this.poolMetrics.peakConnections > 150) {
      recommendations.push('Consider implementing connection queuing');
    }

    return recommendations;
  }

  /**
   * Assess overall connection health
   */
  assessConnectionHealth() {
    const errorRate = this.poolMetrics.connectionErrors / this.poolMetrics.totalConnections;
    const utilizationRate = this.poolMetrics.activeConnections / this.poolMetrics.totalConnections;

    if (errorRate > 0.1) return 'poor';
    if (utilizationRate > 0.9) return 'stressed';
    if (utilizationRate < 0.1) return 'underutilized';

    return 'healthy';
  }

  /**
   * Get current pool status
   */
  getPoolStatus() {
    return {
      ...this.poolMetrics,
      health: this.assessConnectionHealth(),
      utilizationRate: this.poolMetrics.activeConnections / this.poolMetrics.totalConnections,
      memoryUsage: this.getMemoryUsage()
    };
  }
}

module.exports = new ConnectionPoolOptimizer();
