{"name": "kryptopesa-backend", "version": "1.0.0", "description": "Backend API for KryptoPesa P2P trading platform", "main": "src/server.js", "scripts": {"start": "node src/server.js", "dev": "nodemon src/server.js", "build": "echo 'No build step required for Node.js'", "test": "jest --<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "test:watch": "jest --watch", "test:unit": "jest tests/unit --detectOpenHandles --coverage", "test:integration": "jest tests/integration --runInBand --detectOpenHandles --coverage", "test:all": "npm run test:unit && npm run test:integration", "test:ci": "jest --ci --coverage --watchAll=false --detectO<PERSON><PERSON>andles", "test:coverage": "jest --coverage --detectOpenHandles --coverageReporters=html --coverageReporters=text", "test:performance": "jest tests/performance --detect<PERSON><PERSON><PERSON><PERSON><PERSON>", "test:security": "npm audit && npm run test:pentest", "test:pentest": "node tests/security/penetrationTest.js", "test:coverage-check": "jest --coverage --coverageThreshold='{\"global\":{\"branches\":90,\"functions\":90,\"lines\":90,\"statements\":90}}'", "lint": "eslint src/ tests/", "lint:fix": "eslint src/ tests/ --fix", "lint:check": "eslint src/ tests/ --max-warnings 0", "seed": "node src/scripts/seedDatabase.js", "security:validate": "node scripts/validate-security.js", "security:check": "npm run security:validate && npm audit --audit-level moderate", "prestart": "npm run security:validate", "predeploy": "npm run security:check && npm run test:coverage-check", "load-test": "artillery run load-tests/load-test-config.yml", "load-test:quick": "artillery quick --count 100 --num 10 http://localhost:3000/health", "load-test:report": "artillery run load-tests/load-test-config.yml --output load-test-results.json && artillery report load-test-results.json", "performance:test": "npm run load-test:report"}, "dependencies": {"@socket.io/redis-adapter": "^8.3.0", "aws-sdk": "^2.1692.0", "axios": "^1.4.0", "bcryptjs": "^2.4.3", "bip32": "^5.0.0-rc.0", "bitcoinjs-lib": "^6.1.7", "cloudinary": "^1.38.0", "colors": "^1.4.0", "compression": "^1.8.0", "cors": "^2.8.5", "discord-webhook-node": "^1.1.8", "dotenv": "^16.3.1", "ethers": "^5.8.0", "expo-local-authentication": "~16.0.5", "expo-secure-store": "~14.2.3", "express": "^4.18.2", "express-mongo-sanitize": "^2.2.0", "express-rate-limit": "^6.11.2", "express-slow-down": "^2.1.0", "express-validator": "^7.2.1", "file-type": "^16.5.4", "firebase-admin": "^13.4.0", "helmet": "^7.2.0", "hpp": "^0.2.3", "joi": "^17.9.2", "jsonwebtoken": "^9.0.1", "mongoose": "^7.4.1", "morgan": "^1.10.0", "multer": "^1.4.5-lts.1", "node-cron": "^4.2.0", "nodemailer": "^6.10.1", "prom-client": "^15.1.3", "qrcode": "^1.5.4", "rate-limit-redis": "^3.0.1", "react-native-qrcode-svg": "^6.3.15", "redis": "^4.6.7", "sharp": "^0.34.2", "socket.io": "^4.7.2", "socket.io-client": "^4.8.1", "speakeasy": "^2.0.0", "tiny-secp256k1": "^2.2.4", "validator": "^13.11.0", "winston": "^3.10.0", "xss": "^1.0.14", "xss-clean": "^0.1.4"}, "devDependencies": {"@types/jest": "^29.5.3", "artillery": "^2.0.23", "chai": "^5.2.0", "eslint": "^8.45.0", "jest": "^29.6.1", "mongodb-memory-server": "^8.16.1", "nodemon": "^3.0.1", "sinon": "^21.0.0", "supertest": "^6.3.3"}, "jest": {"testEnvironment": "node", "collectCoverageFrom": ["src/**/*.js", "!src/server.js", "!src/__tests__/**"], "testMatch": ["**/__tests__/**/*.test.js"], "testTimeout": 30000, "maxWorkers": 1, "forceExit": true, "detectOpenHandles": true, "clearMocks": true, "resetMocks": true, "restoreMocks": true, "verbose": true}, "engines": {"node": ">=18.0.0"}}