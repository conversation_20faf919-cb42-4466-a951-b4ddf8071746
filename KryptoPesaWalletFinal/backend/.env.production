# KryptoPesa Backend Production Environment Configuration
# Critical: This file contains production settings for enterprise deployment

# Environment
NODE_ENV=production

# Server Configuration
PORT=3000
HOST=0.0.0.0

# Database Configuration (Production)
# CRITICAL: Use secure connection strings with authentication
MONGODB_URI=${MONGODB_URI}
MONGODB_OPTIONS=retryWrites=true&w=majority&readPreference=primary

# Redis Configuration (Production)
# CRITICAL: Must include authentication in production
REDIS_URL=${REDIS_URL}
REDIS_PASSWORD=${REDIS_PASSWORD}
REDIS_DB=0

# Security Configuration
# CRITICAL: These values MUST be set via secure secret management in production
# Use AWS Secrets Manager, HashiCorp Vault, or Kubernetes secrets
JWT_SECRET=${JWT_SECRET}
JWT_EXPIRES_IN=7d
JWT_REFRESH_EXPIRES_IN=30d

# Encryption Keys
# CRITICAL: Must be 32+ character random string, managed via secret store
ENCRYPTION_KEY=${ENCRYPTION_KEY}
HASH_ROUNDS=12

# API Security
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=5
RATE_LIMIT_SKIP_SUCCESSFUL_REQUESTS=false

# CORS Configuration
CORS_ORIGIN=https://app.kryptopesa.com,https://admin.kryptopesa.com
CORS_CREDENTIALS=true

# File Upload Configuration
MAX_FILE_SIZE=10485760
UPLOAD_PATH=/app/uploads
ALLOWED_FILE_TYPES=image/jpeg,image/png,image/gif,application/pdf

# Blockchain Configuration
# CRITICAL: RPC URLs and private keys must be secured via secret management
ETHEREUM_RPC_URL=${ETHEREUM_RPC_URL}
POLYGON_RPC_URL=${POLYGON_RPC_URL}
PRIVATE_KEY=${BLOCKCHAIN_PRIVATE_KEY}

# Smart Contract Addresses (Public addresses, safe to include)
ESCROW_CONTRACT_ADDRESS=${ESCROW_CONTRACT_ADDRESS}
USDT_CONTRACT_ADDRESS=******************************************

# External Services
# CRITICAL: All API credentials must be managed via secret store
CLOUDINARY_CLOUD_NAME=${CLOUDINARY_CLOUD_NAME}
CLOUDINARY_API_KEY=${CLOUDINARY_API_KEY}
CLOUDINARY_API_SECRET=${CLOUDINARY_API_SECRET}

# Email Configuration
# CRITICAL: SMTP credentials must be secured
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_SECURE=false
SMTP_USER=${SMTP_USER}
SMTP_PASS=${SMTP_PASSWORD}

# Push Notifications
# CRITICAL: Firebase credentials must be secured
FCM_SERVER_KEY=${FCM_SERVER_KEY}
FCM_PROJECT_ID=${FCM_PROJECT_ID}

# Monitoring & Logging
LOG_LEVEL=info
LOG_FILE=/app/logs/app.log
ENABLE_REQUEST_LOGGING=true
ENABLE_ERROR_TRACKING=true

# Comprehensive Monitoring Configuration
EMAIL_ALERTS_ENABLED=true
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=CHANGE_ME_IN_PRODUCTION
ALERT_RECIPIENTS=<EMAIL>,<EMAIL>

# Discord Alerts
DISCORD_ALERTS_ENABLED=true
DISCORD_WEBHOOK_URL=CHANGE_ME_IN_PRODUCTION

# Slack Alerts
SLACK_ALERTS_ENABLED=false
SLACK_WEBHOOK_URL=CHANGE_ME_IN_PRODUCTION

# Metrics Authentication
METRICS_TOKEN=kryptopesa_metrics_token_2025_secure

# Performance Configuration
ENABLE_COMPRESSION=true
ENABLE_CACHING=true
CACHE_TTL=3600

# Security Headers
ENABLE_HELMET=true
ENABLE_RATE_LIMITING=true
ENABLE_CORS=true

# Database Connection Pool
DB_MIN_POOL_SIZE=5
DB_MAX_POOL_SIZE=20
DB_CONNECTION_TIMEOUT=30000

# Session Configuration
SESSION_SECRET=your-session-secret-key-for-production
SESSION_COOKIE_SECURE=true
SESSION_COOKIE_HTTP_ONLY=true
SESSION_COOKIE_SAME_SITE=strict

# Health Check Configuration
HEALTH_CHECK_ENABLED=true
HEALTH_CHECK_TIMEOUT=5000

# Metrics Configuration
METRICS_ENABLED=true
METRICS_PORT=9090
PROMETHEUS_ENABLED=true
