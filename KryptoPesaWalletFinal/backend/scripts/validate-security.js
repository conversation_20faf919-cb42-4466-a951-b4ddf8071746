#!/usr/bin/env node

/**
 * Security Validation Script
 * Validates all security configurations and secrets before deployment
 */

const fs = require('fs');
const path = require('path');
const crypto = require('crypto');

// Import our secret manager
const { validateSecrets } = require('../src/config/secrets');

class SecurityValidator {
    constructor() {
        this.errors = [];
        this.warnings = [];
        this.passed = [];
    }

    async validateAll() {
        console.log('🔒 Starting comprehensive security validation...\n');

        await this.validateSecrets();
        await this.validateEnvironmentFiles();
        await this.validateFilePermissions();
        await this.validateDependencies();
        await this.validateSSLConfiguration();

        this.printResults();

        if (this.errors.length > 0) {
            process.exit(1);
        }
    }

    async validateSecrets() {
        console.log('📋 Validating secrets and environment variables...');

        try {
            await validateSecrets();
            this.passed.push('✅ All required secrets are properly configured');
        } catch (error) {
            this.errors.push(`❌ Secret validation failed: ${error.message}`);
        }

        // Check for insecure patterns in environment files
        const envFiles = ['.env', '.env.production', '.env.local'];

        for (const envFile of envFiles) {
            const envPath = path.join(__dirname, '..', envFile);
            if (fs.existsSync(envPath)) {
                const content = fs.readFileSync(envPath, 'utf8');
                this.checkForInsecurePatterns(content, envFile);
            }
        }
    }

    checkForInsecurePatterns(content, filename) {
        const insecurePatterns = [
            { pattern: /password.*=.*[^${}]/i, message: 'Hardcoded password detected' },
            { pattern: /secret.*=.*[^${}]/i, message: 'Hardcoded secret detected' },
            { pattern: /key.*=.*[^${}]/i, message: 'Hardcoded key detected' },
            { pattern: /token.*=.*[^${}]/i, message: 'Hardcoded token detected' },
            { pattern: /your-/i, message: 'Placeholder value detected' },
            { pattern: /CHANGE_ME/i, message: 'Placeholder value detected' },
            { pattern: /123456/i, message: 'Weak credential detected' },
            { pattern: /mongodb:\/\/[^$]/i, message: 'Hardcoded MongoDB URI detected' },
            { pattern: /redis:\/\/[^$]/i, message: 'Hardcoded Redis URI detected' }
        ];

        const lines = content.split('\n');

        lines.forEach((line, index) => {
            if (line.trim().startsWith('#') || !line.includes('=')) return;

            insecurePatterns.forEach(({ pattern, message }) => {
                if (pattern.test(line)) {
                    this.errors.push(`❌ ${filename}:${index + 1} - ${message}: ${line.trim()}`);
                }
            });
        });
    }

    async validateEnvironmentFiles() {
        console.log('📁 Validating environment file security...');

        const envFiles = ['.env', '.env.production', '.env.local'];

        for (const envFile of envFiles) {
            const envPath = path.join(__dirname, '..', envFile);

            if (fs.existsSync(envPath)) {
                const stats = fs.statSync(envPath);
                const mode = stats.mode & parseInt('777', 8);

                if (mode !== parseInt('600', 8) && mode !== parseInt('644', 8)) {
                    this.warnings.push(`⚠️  ${envFile} has insecure permissions: ${mode.toString(8)}`);
                } else {
                    this.passed.push(`✅ ${envFile} has secure permissions`);
                }
            }
        }
    }

    async validateFilePermissions() {
        console.log('🔐 Validating file permissions...');

        const sensitiveFiles = [
            'src/config/secrets.js',
            'src/services/encryption.js',
            'src/config/security.js'
        ];

        for (const file of sensitiveFiles) {
            const filePath = path.join(__dirname, '..', file);

            if (fs.existsSync(filePath)) {
                const stats = fs.statSync(filePath);
                const mode = stats.mode & parseInt('777', 8);

                if (mode > parseInt('644', 8)) {
                    this.warnings.push(`⚠️  ${file} has overly permissive permissions: ${mode.toString(8)}`);
                } else {
                    this.passed.push(`✅ ${file} has appropriate permissions`);
                }
            }
        }
    }

    async validateDependencies() {
        console.log('📦 Validating security-related dependencies...');

        const packagePath = path.join(__dirname, '..', 'package.json');

        if (fs.existsSync(packagePath)) {
            const packageJson = JSON.parse(fs.readFileSync(packagePath, 'utf8'));
            const dependencies = { ...packageJson.dependencies, ...packageJson.devDependencies };

            // Check for security-related packages
            const securityPackages = [
                'helmet',
                'express-rate-limit',
                'express-validator',
                'bcrypt',
                'jsonwebtoken'
            ];

            securityPackages.forEach(pkg => {
                if (dependencies[pkg]) {
                    this.passed.push(`✅ Security package ${pkg} is installed`);
                } else {
                    this.warnings.push(`⚠️  Security package ${pkg} is not installed`);
                }
            });
        }
    }

    async validateSSLConfiguration() {
        console.log('🔒 Validating SSL configuration...');

        if (process.env.NODE_ENV === 'production') {
            const sslCertPath = process.env.SSL_CERT_PATH;
            const sslKeyPath = process.env.SSL_KEY_PATH;

            if (!sslCertPath || !sslKeyPath) {
                this.errors.push('❌ SSL certificate paths not configured for production');
                return;
            }

            if (fs.existsSync(sslCertPath) && fs.existsSync(sslKeyPath)) {
                this.passed.push('✅ SSL certificate files exist');
            } else {
                this.errors.push('❌ SSL certificate files not found');
            }
        } else {
            this.passed.push('✅ SSL validation skipped for non-production environment');
        }
    }

    printResults() {
        console.log('\n' + '='.repeat(60));
        console.log('🔒 SECURITY VALIDATION RESULTS');
        console.log('='.repeat(60));

        if (this.passed.length > 0) {
            console.log('\n✅ PASSED CHECKS:');
            this.passed.forEach(check => console.log(`  ${check}`));
        }

        if (this.warnings.length > 0) {
            console.log('\n⚠️  WARNINGS:');
            this.warnings.forEach(warning => console.log(`  ${warning}`));
        }

        if (this.errors.length > 0) {
            console.log('\n❌ ERRORS (MUST FIX):');
            this.errors.forEach(error => console.log(`  ${error}`));
        }

        console.log('\n' + '='.repeat(60));
        console.log(`📊 SUMMARY: ${this.passed.length} passed, ${this.warnings.length} warnings, ${this.errors.length} errors`);

        if (this.errors.length === 0) {
            console.log('🎉 Security validation completed successfully!');
        } else {
            console.log('🚨 Security validation failed! Please fix the errors above.');
        }
        console.log('='.repeat(60));
    }
}

// Run validation if called directly
if (require.main === module) {
    const validator = new SecurityValidator();
    validator.validateAll().catch(error => {
        console.error('❌ Security validation failed:', error);
        process.exit(1);
    });
}

module.exports = SecurityValidator;