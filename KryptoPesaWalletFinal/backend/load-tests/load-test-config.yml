config:
  target: 'http://localhost:3000'
  phases:
    # Warm-up phase
    - duration: 60
      arrivalRate: 10
      name: "Warm-up"
    # Ramp-up phase
    - duration: 300
      arrivalRate: 50
      rampTo: 200
      name: "Ramp-up to normal load"
    # Sustained load phase (simulating 10,000 concurrent users)
    - duration: 600
      arrivalRate: 200
      name: "Sustained normal load"
    # Peak load phase (simulating 50,000+ daily active users)
    - duration: 300
      arrivalRate: 500
      rampTo: 1000
      name: "Peak load simulation"
    # Stress test phase
    - duration: 180
      arrivalRate: 1000
      name: "Stress test"
    # Cool-down phase
    - duration: 120
      arrivalRate: 1000
      rampTo: 10
      name: "Cool-down"

  # Performance thresholds
  ensure:
    - http.response_time.p95: 500  # 95% of requests under 500ms
    - http.response_time.p99: 1000 # 99% of requests under 1s
    - http.response_time.median: 200 # Median response time under 200ms
    - http.codes.200: 95 # 95% success rate minimum
    - http.codes.500: 1  # Less than 1% server errors

  # Load test configuration
  http:
    timeout: 30
    pool: 50

  # Metrics collection
  plugins:
    metrics-by-endpoint:
      useOnlyRequestNames: true

  # Environment variables for testing
  variables:
    testUserId: "test-user-{{ $randomString() }}"
    testEmail: "test-{{ $randomString() }}@example.com"
    testWalletAddress: "0x{{ $randomString(40) }}"

scenarios:
  # Authentication flow testing
  - name: "User Authentication Flow"
    weight: 20
    flow:
      - post:
          url: "/api/auth/register"
          json:
            email: "{{ testEmail }}"
            password: "TestPassword123!"
            walletAddress: "{{ testWalletAddress }}"
          capture:
            - json: "$.token"
              as: "authToken"
            - json: "$.user.id"
              as: "userId"
      - post:
          url: "/api/auth/login"
          json:
            email: "{{ testEmail }}"
            password: "TestPassword123!"
          expect:
            - statusCode: 200

  # Trading operations testing
  - name: "Trading Operations"
    weight: 30
    flow:
      - post:
          url: "/api/auth/login"
          json:
            email: "<EMAIL>"
            password: "TestPassword123!"
          capture:
            - json: "$.token"
              as: "authToken"
      - get:
          url: "/api/offers"
          headers:
            Authorization: "Bearer {{ authToken }}"
          expect:
            - statusCode: 200
      - post:
          url: "/api/offers"
          headers:
            Authorization: "Bearer {{ authToken }}"
          json:
            type: "buy"
            cryptocurrency: "USDT"
            fiatCurrency: "KES"
            amount: "{{ $randomInt(10, 1000) }}"
            rate: "{{ $randomInt(140, 160) }}"
            paymentMethods: ["M-Pesa"]
            terms: "Quick trade, online now"
          expect:
            - statusCode: 201

  # Wallet operations testing
  - name: "Wallet Operations"
    weight: 25
    flow:
      - post:
          url: "/api/auth/login"
          json:
            email: "<EMAIL>"
            password: "TestPassword123!"
          capture:
            - json: "$.token"
              as: "authToken"
      - get:
          url: "/api/wallet/balance"
          headers:
            Authorization: "Bearer {{ authToken }}"
          expect:
            - statusCode: 200
      - get:
          url: "/api/wallet/transactions"
          headers:
            Authorization: "Bearer {{ authToken }}"
          expect:
            - statusCode: 200

  # Chat and messaging testing
  - name: "Chat Operations"
    weight: 15
    flow:
      - post:
          url: "/api/auth/login"
          json:
            email: "<EMAIL>"
            password: "TestPassword123!"
          capture:
            - json: "$.token"
              as: "authToken"
      - get:
          url: "/api/chats"
          headers:
            Authorization: "Bearer {{ authToken }}"
          expect:
            - statusCode: 200
      - post:
          url: "/api/chats/test-chat-id/messages"
          headers:
            Authorization: "Bearer {{ authToken }}"
          json:
            content: "Test message {{ $randomString() }}"
            type: "text"
          expect:
            - statusCode: 201

  # System health and monitoring
  - name: "System Health Checks"
    weight: 10
    flow:
      - get:
          url: "/health"
          expect:
            - statusCode: 200
      - get:
          url: "/api/system/status"
          expect:
            - statusCode: 200