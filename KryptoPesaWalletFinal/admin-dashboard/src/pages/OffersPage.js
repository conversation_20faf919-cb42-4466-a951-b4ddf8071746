import React, { useState, useEffect } from 'react';
import {
const logger = require('./utils/logger');

Box,
  Typography,
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  TablePagination,
  Chip,
  IconButton,
  Tooltip,
  TextField,
  InputAdornment,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Grid,
  Card,
  CardContent,
  Button,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Avatar,
} from '@mui/material';
import {
  Search as SearchIcon,
  Visibility as ViewIcon,
  Block as BlockIcon,
  CheckCircle as ActivateIcon,
  Pause as PauseIcon,
  TrendingUp as TrendingUpIcon,
  TrendingDown as TrendingDownIcon,
} from '@mui/icons-material';
import axios from 'axios';

const OffersPage = () => {
  const [offers, setOffers] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(25);
  const [totalOffers, setTotalOffers] = useState(0);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('');
  const [typeFilter, setTypeFilter] = useState('');
  const [cryptoFilter, setCryptoFilter] = useState('');
  const [selectedOffer, setSelectedOffer] = useState(null);
  const [offerDetailOpen, setOfferDetailOpen] = useState(false);

  // Fetch offers data
  const fetchOffers = async () => {
    try {
      setLoading(true);
      const params = {
        page: page + 1,
        limit: rowsPerPage,
        search: searchTerm || undefined,
        status: statusFilter || undefined,
        type: typeFilter || undefined,
        cryptocurrency: cryptoFilter || undefined,
        sortBy: 'createdAt',
        sortOrder: 'desc'
      };

      const response = await axios.get('/admin/offers', { params });

      if (response.data.success) {
        setOffers(response.data.data.offers);
        setTotalOffers(response.data.data.pagination.totalOffers);
      }
    } catch (error) {
      // logger.error('Error fetching offers:', error);
      setError('Failed to fetch offers');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchOffers();
  }, [page, rowsPerPage, searchTerm, statusFilter, typeFilter, cryptoFilter]);

  const handleChangePage = (event, newPage) => {
    setPage(newPage);
  };

  const handleChangeRowsPerPage = (event) => {
    setRowsPerPage(parseInt(event.target.value, 10));
    setPage(0);
  };

  const handleViewOffer = (offer) => {
    setSelectedOffer(offer);
    setOfferDetailOpen(true);
  };

  const handleOfferAction = async (offer, action) => {
    try {
      const response = await axios.put(`/admin/offers/${offer.offerId}/status`, {
        status: action,
        reason: `Admin ${action} action`
      });

      if (response.data.success) {
        fetchOffers(); // Refresh the list
      }
    } catch (error) {
      // logger.error('Error performing offer action:', error);
    }
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'active': return 'success';
      case 'paused': return 'warning';
      case 'inactive': return 'error';
      case 'expired': return 'default';
      default: return 'default';
    }
  };

  const getTypeColor = (type) => {
    return type === 'buy' ? 'success' : 'error';
  };

  const getTypeIcon = (type) => {
    return type === 'buy' ? <TrendingUpIcon /> : <TrendingDownIcon />;
  };

  const formatAmount = (amount, currency) => {
    return `${parseFloat(amount).toFixed(2)} ${currency}`;
  };

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const calculateMargin = (effectivePrice, marketPrice) => {
    if (!marketPrice) return 'N/A';
    const margin = ((effectivePrice - marketPrice) / marketPrice) * 100;
    return `${margin > 0 ? '+' : ''}${margin.toFixed(2)}%`;
  };

  return (
    <Box>
      <Typography variant="h4" gutterBottom>
        Offer Management
      </Typography>

      {/* Summary Cards */}
      <Grid container spacing={3} mb={3}>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Typography color="textSecondary" gutterBottom>
                Total Offers
              </Typography>
              <Typography variant="h4">
                {totalOffers.toLocaleString()}
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Typography color="textSecondary" gutterBottom>
                Active Offers
              </Typography>
              <Typography variant="h4" color="success.main">
                {offers.filter(o => o.status === 'active').length}
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Typography color="textSecondary" gutterBottom>
                Buy Offers
              </Typography>
              <Typography variant="h4" color="success.main">
                {offers.filter(o => o.type === 'buy').length}
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Typography color="textSecondary" gutterBottom>
                Sell Offers
              </Typography>
              <Typography variant="h4" color="error.main">
                {offers.filter(o => o.type === 'sell').length}
              </Typography>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Filters */}
      <Paper sx={{ p: 2, mb: 3 }}>
        <Grid container spacing={2} alignItems="center">
          <Grid item xs={12} md={3}>
            <TextField
              fullWidth
              placeholder="Search offers..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              InputProps={{
                startAdornment: (
                  <InputAdornment position="start">
                    <SearchIcon />
                  </InputAdornment>
                ),
              }}
            />
          </Grid>
          <Grid item xs={12} md={2}>
            <FormControl fullWidth>
              <InputLabel>Status</InputLabel>
              <Select
                value={statusFilter}
                onChange={(e) => setStatusFilter(e.target.value)}
                label="Status"
              >
                <MenuItem value="">All</MenuItem>
                <MenuItem value="active">Active</MenuItem>
                <MenuItem value="paused">Paused</MenuItem>
                <MenuItem value="inactive">Inactive</MenuItem>
                <MenuItem value="expired">Expired</MenuItem>
              </Select>
            </FormControl>
          </Grid>
          <Grid item xs={12} md={2}>
            <FormControl fullWidth>
              <InputLabel>Type</InputLabel>
              <Select
                value={typeFilter}
                onChange={(e) => setTypeFilter(e.target.value)}
                label="Type"
              >
                <MenuItem value="">All</MenuItem>
                <MenuItem value="buy">Buy</MenuItem>
                <MenuItem value="sell">Sell</MenuItem>
              </Select>
            </FormControl>
          </Grid>
          <Grid item xs={12} md={2}>
            <FormControl fullWidth>
              <InputLabel>Crypto</InputLabel>
              <Select
                value={cryptoFilter}
                onChange={(e) => setCryptoFilter(e.target.value)}
                label="Crypto"
              >
                <MenuItem value="">All</MenuItem>
                <MenuItem value="USDT">USDT</MenuItem>
                <MenuItem value="USDC">USDC</MenuItem>
                <MenuItem value="BTC">BTC</MenuItem>
                <MenuItem value="ETH">ETH</MenuItem>
              </Select>
            </FormControl>
          </Grid>
          <Grid item xs={12} md={2}>
            <Button
              variant="outlined"
              onClick={() => {
                setSearchTerm('');
                setStatusFilter('');
                setTypeFilter('');
                setCryptoFilter('');
              }}
            >
              Clear Filters
            </Button>
          </Grid>
        </Grid>
      </Paper>

      {/* Offers Table */}
      <TableContainer component={Paper}>
        <Table>
          <TableHead>
            <TableRow>
              <TableCell>Offer ID</TableCell>
              <TableCell>Creator</TableCell>
              <TableCell>Type</TableCell>
              <TableCell>Amount Range</TableCell>
              <TableCell>Price</TableCell>
              <TableCell>Status</TableCell>
              <TableCell>Created</TableCell>
              <TableCell>Actions</TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {offers.map((offer) => (
              <TableRow key={offer._id} hover>
                <TableCell>
                  <Typography variant="body2" fontFamily="monospace">
                    {offer.offerId}
                  </Typography>
                </TableCell>
                <TableCell>
                  <Box display="flex" alignItems="center" gap={1}>
                    <Avatar sx={{ width: 32, height: 32 }}>
                      {offer.creator?.username?.charAt(0).toUpperCase()}
                    </Avatar>
                    <Box>
                      <Typography variant="body2">
                        {offer.creator?.username}
                      </Typography>
                      <Typography variant="caption" color="textSecondary">
                        Rep: {offer.creator?.reputation?.score || 0}
                      </Typography>
                    </Box>
                  </Box>
                </TableCell>
                <TableCell>
                  <Chip
                    icon={getTypeIcon(offer.type)}
                    label={offer.type.toUpperCase()}
                    color={getTypeColor(offer.type)}
                    size="small"
                  />
                </TableCell>
                <TableCell>
                  <Box>
                    <Typography variant="body2">
                      {formatAmount(offer.cryptocurrency?.minAmount, offer.cryptocurrency?.symbol)} -
                      {formatAmount(offer.cryptocurrency?.maxAmount, offer.cryptocurrency?.symbol)}
                    </Typography>
                    <Typography variant="caption" color="textSecondary">
                      Available: {formatAmount(offer.cryptocurrency?.availableAmount, offer.cryptocurrency?.symbol)}
                    </Typography>
                  </Box>
                </TableCell>
                <TableCell>
                  <Box>
                    <Typography variant="body2">
                      {formatAmount(offer.fiat?.effectivePrice, offer.fiat?.currency)}
                    </Typography>
                    <Typography variant="caption" color="textSecondary">
                      Margin: {calculateMargin(offer.fiat?.effectivePrice, offer.fiat?.marketPrice)}
                    </Typography>
                  </Box>
                </TableCell>
                <TableCell>
                  <Chip
                    label={offer.status}
                    color={getStatusColor(offer.status)}
                    size="small"
                  />
                </TableCell>
                <TableCell>
                  <Typography variant="body2">
                    {formatDate(offer.createdAt)}
                  </Typography>
                </TableCell>
                <TableCell>
                  <Box display="flex" gap={1}>
                    <Tooltip title="View Details">
                      <IconButton
                        size="small"
                        onClick={() => handleViewOffer(offer)}
                      >
                        <ViewIcon />
                      </IconButton>
                    </Tooltip>

                    {offer.status === 'active' && (
                      <Tooltip title="Pause Offer">
                        <IconButton
                          size="small"
                          color="warning"
                          onClick={() => handleOfferAction(offer, 'paused')}
                        >
                          <PauseIcon />
                        </IconButton>
                      </Tooltip>
                    )}

                    {offer.status === 'paused' && (
                      <Tooltip title="Activate Offer">
                        <IconButton
                          size="small"
                          color="success"
                          onClick={() => handleOfferAction(offer, 'active')}
                        >
                          <ActivateIcon />
                        </IconButton>
                      </Tooltip>
                    )}

                    <Tooltip title="Deactivate Offer">
                      <IconButton
                        size="small"
                        color="error"
                        onClick={() => handleOfferAction(offer, 'inactive')}
                      >
                        <BlockIcon />
                      </IconButton>
                    </Tooltip>
                  </Box>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>

        <TablePagination
          rowsPerPageOptions={[10, 25, 50, 100]}
          component="div"
          count={totalOffers}
          rowsPerPage={rowsPerPage}
          page={page}
          onPageChange={handleChangePage}
          onRowsPerPageChange={handleChangeRowsPerPage}
        />
      </TableContainer>

      {/* Offer Detail Dialog */}
      <Dialog
        open={offerDetailOpen}
        onClose={() => setOfferDetailOpen(false)}
        maxWidth="md"
        fullWidth
      >
        <DialogTitle>
          Offer Details - {selectedOffer?.offerId}
        </DialogTitle>
        <DialogContent>
          {selectedOffer && (
            <Box>
              <Grid container spacing={2}>
                <Grid item xs={12} md={6}>
                  <Typography variant="h6" gutterBottom>
                    Offer Information
                  </Typography>
                  <Typography><strong>Type:</strong> {selectedOffer.type}</Typography>
                  <Typography><strong>Status:</strong> {selectedOffer.status}</Typography>
                  <Typography><strong>Created:</strong> {formatDate(selectedOffer.createdAt)}</Typography>
                  <Typography><strong>Expires:</strong> {formatDate(selectedOffer.expiresAt)}</Typography>
                </Grid>
                <Grid item xs={12} md={6}>
                  <Typography variant="h6" gutterBottom>
                    Amount & Pricing
                  </Typography>
                  <Typography>
                    <strong>Min:</strong> {formatAmount(selectedOffer.cryptocurrency?.minAmount, selectedOffer.cryptocurrency?.symbol)}
                  </Typography>
                  <Typography>
                    <strong>Max:</strong> {formatAmount(selectedOffer.cryptocurrency?.maxAmount, selectedOffer.cryptocurrency?.symbol)}
                  </Typography>
                  <Typography>
                    <strong>Available:</strong> {formatAmount(selectedOffer.cryptocurrency?.availableAmount, selectedOffer.cryptocurrency?.symbol)}
                  </Typography>
                  <Typography>
                    <strong>Price:</strong> {formatAmount(selectedOffer.fiat?.effectivePrice, selectedOffer.fiat?.currency)}
                  </Typography>
                </Grid>
                <Grid item xs={12}>
                  <Typography variant="h6" gutterBottom>
                    Creator Information
                  </Typography>
                  <Typography>
                    <strong>Username:</strong> {selectedOffer.creator?.username}
                  </Typography>
                  <Typography>
                    <strong>Reputation:</strong> {selectedOffer.creator?.reputation?.score || 0}/100
                  </Typography>
                  <Typography>
                    <strong>Completed Trades:</strong> {selectedOffer.creator?.reputation?.completedTrades || 0}
                  </Typography>
                </Grid>
                <Grid item xs={12}>
                  <Typography variant="h6" gutterBottom>
                    Payment Methods
                  </Typography>
                  {selectedOffer.paymentMethods?.map((method, index) => (
                    <Chip
                      key={index}
                      label={method.method.replace('_', ' ')}
                      style={{ marginRight: 8, marginBottom: 4 }}
                    />
                  ))}
                </Grid>
                {selectedOffer.terms?.instructions && (
                  <Grid item xs={12}>
                    <Typography variant="h6" gutterBottom>
                      Instructions
                    </Typography>
                    <Typography>{selectedOffer.terms.instructions}</Typography>
                  </Grid>
                )}
              </Grid>
            </Box>
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setOfferDetailOpen(false)}>
            Close
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default OffersPage;
